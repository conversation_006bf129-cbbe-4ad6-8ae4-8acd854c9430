<?php

$show_legacy_nav = $_SESSION['show_legacy_nav'];

$displayNavLink = '';
$hideAllHeader = '';
if($show_legacy_nav == 0){
	$displayNavLink = 'style="display:none;"';
	//hide nav menu link for web display only.

	//if page is loaded in iframe
}

if(isset($_REQUEST['show_header']) && $_REQUEST['show_header'] == 0){
		$hideAllHeader = 'style="display:none;"';
		//hide all header and link
		//this is used in ifram
		//no space
		echo '<style>';
		echo '#headerfixedmenuspacer { display:none; }';
		echo '</style>';
	}
?>


<nav class="navbar navbar-default navbar-fixed-top navbar-prop" <?= $displayNavLink ?>>
		
		<?php
		$qsp=doQuery("SELECT * FROM web_mf_partners WHERE sp_id='".$_SESSION['sp_id']."' LIMIT 1 ");
		$res=mysql_fetch_array($qsp);

        if(IsClientAdmin() || IsAdmin() || IsClient() || IsSP() || IsCourier() || IsSPCourier()) {
			if(!IsClient() && !IsFromMobileApp() && !IsSP() && !IsCourier() && !IsSPCourier()) {
		$query=doQuery("SELECT Last_Rundate as date
					    FROM web_api_util_status WHERE ApiType=0 ORDER BY Last_Rundate DESC LIMIT 1");
		$row=mysql_fetch_array($query);
	
	    $query=doQuery("SELECT Last_Rundate as date
					    FROM web_api_util_status WHERE ApiType=1 ORDER BY RUN_Date DESC LIMIT 1");
					    $row_one=mysql_fetch_array($query);				
		?>
		<div class="apistatus">
		<div id="statustext">
		<a title="API sync status" href="javascript:ShowUploadPOD('lds_util.php?utilstatus=a776458e0427f52ef6a0f09e5afe1ed9fd08e12d','API Sync Status and Last Run')">
		API Last synced
		<?php echo ':&nbsp;'.FormatDate($row['date'],5)  ?>
		</a>
		<a title="SMS date & time last synced - Click to view the history" href="javascript:ShowSmsInbox('tab_sms_inbox.php','SMS Inbox Report')"><?php echo '&nbsp;&nbsp;SMS:&nbsp;'.FormatDate($row_one['date'],5) ?></a>
		</div>
		<br />
		<?php
			} else {
		?>
		<div class="apistatus">
		<div id="statustext">
		</div>
		<br/>
		<?php
			}
			
		$searchval = '';
		$searchoptselected = '';
	    if(strpos($_SERVER['PHP_SELF'],'editpage.php')!==false && isset($_GET['waybillno']) && trim($_GET['waybillno'])!='') {
		    $searchval = $_GET['waybillno'];
			$searchoptselected = 1;
		} elseif(strpos($_SERVER['PHP_SELF'],'editpage.php')!==false && isset($_GET['tripno']) && trim($_GET['tripno'])!='') {
		    $searchval = $_GET['tripno'];
			$searchoptselected = 3;
		} elseif(strpos($_SERVER['PHP_SELF'],'editpage.php')!==false && isset($_GET['orderno']) && trim($_GET['orderno'])!='') {
		    $searchval = $_GET['orderno'];
			$searchoptselected = 4;
		}  elseif(strpos($_SERVER['PHP_SELF'],'editpage.php')!==false && isset($_GET['csmno']) && trim($_GET['csmno'])!='') {
		    $searchval = $_GET['csmno'];
			$searchoptselected = 7;
		} elseif(strpos($_SERVER['PHP_SELF'],'editpage.php')!==false && isset($_GET['drno']) && trim($_GET['drno'])!='') {
		    $searchval = $_GET['drno'];
			$searchoptselected = 8;
		} elseif(strpos($_SERVER['PHP_SELF'],'manage_csm.php')!==false  && isset($_GET['csmno']) && trim($_GET['csmno'])!='') {
			$searchval =$_GET['csmno'];
			$searchoptselected = 2;
		} elseif(strpos($_SERVER['PHP_SELF'],'tab_billing.php')!==false  && isset($_GET['billinginvnum']) && trim($_GET['billinginvnum'])!='') {
			$searchval =$_GET['billinginvnum'];
			$searchoptselected = 5;
		} elseif(strpos($_SERVER['PHP_SELF'],'tab_soa.php')!==false  && isset($_GET['billinginvnum']) && trim($_GET['billinginvnum'])!='') {
			$searchval =$_GET['billinginvnum'];
			$searchoptselected = 5;
		}
		if(!IsFromMobileApp()) {
			$title_input = '';
		?>
		<?php if(IsAdmin() || IsClientAdmin()) { ?>
			<form id="searchwaybillcsm">
			<div class="row">
			<div class="col-sm-5" title="Quick Search for Waybill, CSM, Trip# and Order#">
			<?php if(!IsClient() && !IsSP() && !IsCourier() && !IsSPCourier()) { ?>
			<select title="Quick Search for Waybill, CSM, Trip#, Order#, Billing Invoice#" id="searchFldType" style="color:black;height:20.5px;">
				<option value="1" <?php echo ($searchoptselected==1) ? 'selected' : ''?>>Waybill#</option>
				<option value="2" <?php echo ($searchoptselected==2) ? 'selected' : ''?>>CSM#</option>
				<option value="6" <?php echo ($searchoptselected==6) ? 'selected' : ''?>>DR/Gatepass#</option>
				<option value="3" <?php echo ($searchoptselected==3) ? 'selected' : ''?>>Trip#</option>
				<option value="4" <?php echo ($searchoptselected==4) ? 'selected' : ''?>>WMS / SO Order#</option>
				<option value="5" <?php echo ($searchoptselected==5) ? 'selected' : ''?>>Billing Invoice#</option>
				<option value="7" <?php echo ($searchoptselected==7) ? 'selected' : ''?>>Waybills Consolidation (CSM)</option>
				<option value="9" <?php echo ($searchoptselected==7) ? 'selected' : ''?>>CSM Consolidation (Masster CSM)</option>
				<option value="8" <?php echo ($searchoptselected==8) ? 'selected' : ''?>>DR Waybills</option>
				
			</select>
			<?php } elseif(IsSP()) { ?>
			   <select title="Quick Search for Waybill, CSM, Order#" id="searchFldType" style="width:98px;color:black;height:20.5px;">
				<option value="1" <?php echo ($searchoptselected==1) ? 'selected' : ''?>>Waybill#</option>
				<option value="2" <?php echo ($searchoptselected==2) ? 'selected' : ''?>>CSM#</option>
				<option value="4" <?php echo ($searchoptselected==4) ? 'selected' : ''?>>Order#</option>
			</select>
			<?php } elseif(IsSPCourier()) { ?>
			   <select title="Quick Search for Waybill, CSM, Order#" id="searchFldType" style="width:120px;color:black;height:20.5px;">
				<option value="1" <?php echo ($searchoptselected==1) ? 'selected' : ''?>>Waybill#</option>
				<option value="4" <?php echo ($searchoptselected==4) ? 'selected' : ''?>>Order#</option>
			</select>
			<?php   } elseif(IsCourier()) { ?>
			   <select title="Quick Search for Waybill, CSM, Trip#, Order#, Billing Invoice#" id="searchFldType" style="width:100px;color:black;height:20.5px;">
				<option value="1" <?php echo ($searchoptselected==1) ? 'selected' : ''?>>Waybill#</option>
				<option value="2" <?php echo ($searchoptselected==2) ? 'selected' : ''?>>CSM#</option>
				<option value="6" <?php echo ($searchoptselected==6) ? 'selected' : ''?>>DR/Gatepass#</option>
				<option value="4" <?php echo ($searchoptselected==4) ? 'selected' : ''?>>Order#</option>
			</select>
			 <?php  } else {
				$title_input = 'Billing Invoice#';
				?>
			   <select title="Quick Search for Waybill, CSM, Trip# and Order#" id="searchFldType" style="display:none;color:black;height:20.5px;">
				<option value="5"></option>
			   </select>
			 <?php  }  ?>
			</div>
			<div class="col-sm-5">
			<input type="text" placeholder="<?php echo $title_input ?>" name="waybillcsmsearch" id="waybillcsmsearch" style="color: black;" value="<?php echo $searchval  ?>">
			</div>
			<div class="col-sm-2">
			<button type="submit" style="color:black;" ><i class="glyphicon glyphicon-search"></i></button>
			</div>
			</div>
			</form>
		<?php } ?>
		</div>

		<?php }
		} ?>
		
	<div class="logo_section"> 
	    <?php echo GiveLogoFile(2); ?>
		<img id="logomybiz" src="images/mybizlogo.png"/>
	</div>	
	
		<div class="client_logo_section">
			<?php 
			$query=doQuery("SELECT mfa.account,
							mfa.hubcode,
							mfa.name,
							wma.logo
						FROM mf_accounts mfa
						LEFT JOIN web_mf_accounts wma ON mfa.hubcode=wma.hubcode AND mfa.account=wma.account
						WHERE wma.account='".LWLI_CLIENT_ONLINE."'");
				$row=mysql_fetch_array($query);
				if (IsAdmin() || IsClientAdmin()) { 
					if(IsFromMobileApp()) {
					  echo '<span style="dispaly:block;float:right;margin:10px;font-weight:600;color:#ffff00">'.LWLI_CLIENT_ONLINE.'</span>';
					} else {
					  if(LWLI_CLIENT_ONLINE==ADMIN_USERS_COMPANY) { ?>
						<b><span style="color: #ffff00; margin-right:25px;display:block;">All Clients</span></b>
					  <?php } elseif(IsAdmin() || (IsClientAdmin() && ValidateUserPermission(Admin_GroupID_LoginAsClient))) {
							echo '<a title="View Client Account Page" href="/?viewClientAccount='.LWLI_CLIENT_ONLINE.'"><span style="color: #ccc;font-weight:600;text-decoration:underline;">';
							if((!isset($show_client_logo)) || (isset($show_client_logo) && $show_client_logo)) {
							   echo '<img id="client_logo" style="margin-top:4px;max-height:30px; width: auto;" src="data:image/png;base64,'.base64_encode( $row['logo'] ).'" alt="'.$row['account'].'"  title="'.$row['name'].'" />';
							} else {
							   echo '<b><span title="'.$row['name'].'" style="color:#ffff00;margin-right:15px;display:block;">'.$row['account'].'</span></b>';
							}
							echo '</a>';
						  } else {
							echo '<span title="'.$row['name'].'" style="color:#ffff00;margin-right:15px;display:block;">';
							if((!isset($show_client_logo)) || (isset($show_client_logo) && $show_client_logo)) {
							  echo '<img id="client_logo" style="margin-top:4px;max-height:30px; width: auto;" src="data:image/png;base64,'.base64_encode( $row['logo'] ).'" alt="'.$row['account'].'"  title="'.$row['name'].'" />';
							} else {
								echo $row['account'];
							}
							echo '</span>';
						  }
					}
				}  elseif(IsSPCourier() || IsSP()) { ?>
					<b><span id="sp_name_top_menu" style="color: #ffff00; margin-right:15px;"><?php echo (IsSPCourier()) ? $res['sp_name'] : ''; ?></span></b>
				<?php
				} elseif(IsCourierDriver() || IsCourierEmployee() || IsCourierContractor()) { ?>
				<?php
				} else {
					echo '<span title="'.$row['name'].'" style="font-weight:500;color:#ffff00;margin:5px 15px;display:block;">';
					if(((!isset($show_client_logo)) || (isset($show_client_logo) && $show_client_logo)) && trim($row['logo'])!='') {
					   echo '<img id="client_logo" style="margin-top:4px;max-height:30px; width: auto;" src="data:image/png;base64,'.base64_encode( $row['logo'] ).'" alt="'.$row['account'].'"  title="'.$row['name'].'" />';
					} else {
					   echo ucwords(strtolower($row['name'])," -").' ('.$row['account'].')';
					}
					echo '</span>';
				}
				?>
		</div>

	
		<div class="container container100" <?=$hideAllHeader ?>>
			<div class="navbar-header"> 
				<button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar" aria-expanded="false" aria-controls="navbar">
				<span class="sr-only">Toggle navigation</span>
				<span class="icon-bar"></span>
				<span class="icon-bar"></span>
				<span class="icon-bar"></span>
				</button>
			</div>
			<div id="navbar" class="navbar-collapse collapse">
				<ul class="nav navbar-nav navbar-left">
						<?php if(IsClient()) { ?>
						<li title="Home"><a href="main.php?op=1"><i class="icon-home navmenu-ico"></i></a></li>
					<?php } elseif (IsSP()) { ?>
						<li title="Home"><a href="main.php?op=2"><i class="icon-home navmenu-ico"></i></a></li>
					<?php } elseif (IsDepartment()) { ?>
						<li title="Home"><a href="main.php?op=3"><i class="icon-home navmenu-ico"></i></a></li>
					<?php } elseif (IsAdmin() || IsClientAdmin()) {	?>
						<li title="Home"><a href="main.php?op=0"><i class="icon-home navmenu-ico"></i></a></li>
					<?php } elseif(IsCourierDriver() || IsCourierEmployee()) { ?>
						<li title="Home"><a href="main.php?op=5"><i class="icon-home navmenu-ico"></i></a></li>
					<?php
					} else {
						 header('location:'.Domain_Location.'/index.php');
					} ?>
					
					<li class="dropdown">
					<a href="#" title="Settings" class="dropdown-toggle" data-toggle="dropdown" role="button" aria-expanded="false"><i class="icon-cog navsubmenu-ico"></i><span class="caret navmenu-ico"></span></a>
					<ul class="dropdown-menu">
					<?php if((IsAdmin()) || (IsClientAdmin()) || (IsCourierEmployee())) { ?>
					<li><a href="javascript:ShowSmsInbox('howto/employees_handbook.pdf','Employees Hankbook')">Employees Handbook</a></li>	
					<?php } ?>
					<li><a href="javascript:ShowSmsInbox('https://www.lambertwilliams.com/privacy-policy/','Privacy Policy')">Privacy Policy</a></li>
					<?php if((IsAdmin()) || ($_SESSION['userid']!='65' && (IsClientAdmin() || IsClient()) && (ValidateUserPermission(Client_GroupID_Product_Registration) || ValidateUserPermission(Client_GroupID_Product_List) || ValidateUserPermission(Client_GroupID_User_Registration) || ValidateUserPermission(Client_GroupID_User_List) || ValidateUserPermission(Admin_GroupID_Product_Registration) || ValidateUserPermission(Admin_GroupID_Product_List) || ValidateUserPermission(Admin_GroupID_User_Registration) || ValidateUserPermission(Admin_GroupID_User_List) || ValidateUserPermission(Admin_GroupID_User_List)))) { ?>
					
						<?php
						/*
						if($_SESSION['userid']==11) { ?> 
						<!-- <li><a href="util_itempricing_script.php">util script</a></li> -->
						<?php } ?>
						<!-- <li><a href="EditableInvoice/index.php">invoice</a></li> -->
						<!-- <li><a href="prealerttest.php">actual pre-alert vs pjl test pre-alert</a></li> IsPrimer-->
						<!-- <li><a href="dashboard.php">Admin Dashboard</a></li> -->
						*/ ?>
						
						<?php if(IsAdmin() || IsClientAdmin() || (IsClient() && ValidateUserPermission(Client_GroupID_Product_Registration)) ) { ?>
						<li><a href="product.php?showtab=2">Products</a></li>
						<?php } else if(IsAdmin() || IsClientAdmin() || (IsClient() && ValidateUserPermission(Client_GroupID_Product_List)) ) { ?>
						<li><a href="product.php?showtab=2">Products</a></li>
						<?php } ?>
						<?php if(IsAdmin() || IsClientAdmin()) { ?>
						<li><a href="manage_sp.php?spcData=1&showtab=2">Service Partner</a></li>
						<li><a href="manage_sp.php?spcData=2&showtab=2">In-house Courier</a></li>
						<!-- <li><a href="manage_sp.php?spcData=3&showtab=2">Contractor Courier</a></li> -->
						<?php } ?>
						<?php if(IsAdmin() || IsClientAdmin() || IsSP()) { ?>
						<li><a href="manage_driver.php?showtab=2">Drivers and Helpers</a></li>
						<li><a href="manage_truck.php">Trucks</a></li>
						<?php } ?>
						<?php if(IsAdmin() || IsClientAdmin() || (IsClient() && ValidateUserPermission(Client_GroupID_User_Registration)) ) { ?>
						<li><a href="manage_user.php?showtab=1">User</a></li>
						<?php } else if(IsAdmin() || IsClientAdmin() || (IsClient() && ValidateUserPermission(Client_GroupID_User_List)) ) { ?>
						<li><a href="manage_user.php?showtab=2">User</a></li>
						<?php } ?>
						<?php if((IsAdmin() && ValidateUserPermission(All_GroupID_Show_FacilityArea)) || (IsClientAdmin()  && ValidateUserPermission(Admin_GroupID_Show_FacilityArea)) ) { ?>
						<li><a href="facility_area.php">Facility Area</a></li>
						<?php } ?>
						<?php if(IsAdmin() ) { ?>
						<li><a href="delivery_base_rate.php">Delivery Base Rate Setting</a></li>
						<?php } ?>
						<?php if(IsAdmin() ) { ?>
						<li><a href="delivery_rate_generator.php">Delivery Rate Generator</a></li>
						<?php } ?>
						<?php if(IsAdmin() ) { ?>
						<li><a href="delivery_rate_list.php">Delivery Rate List</a></li>
						<?php } ?>
						<?php if((IsAdmin() && ValidateUserPermission(All_GroupID_DownloadTramsApp)) || (IsClientAdmin() && ValidateUserPermission(Admin_GroupID_DownloadTramsApp)) || (IsSp() && ValidateUserPermission(SP_GroupID_DownloadTramsApp))) { ?>
						<li><a href="MobileApp/lwli_trams.apk"><i class="icon icon-download icon-large"></i>Download Mobile App</a></li>
						<?php } ?>
				
					
			<?php } elseif(IsSP() || IsCourierDriver() || IsCourierEmployee()) { ?>
			<li><a href="MobileApp/lwli_trams.apk"><i class="icon icon-download icon-large"></i>Download Mobile App</a></li>
			<?php } ?>
			
			</ul>
			</li>
		
	<!--<li><a href="wms.php">WMS</a></li>-->
	
	<?php if(IsAdmin() || IsClientAdmin() || (IsClient() && ValidateUserPermission(Client_GroupID_ListOfConsolidation)) || (IsCourierDriver()) || (IsCourierEmployee()) ) { ?>
	<li class="dropdown">
		<a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button"  aria-expanded="false">Transactions<span class="caret"></span></a>
		<ul class="dropdown-menu">
			<li><a href="prealert_dispatching.php">Pre-alert Dispatching</a></li>
		<?php if((IsAdmin() && ValidateUserPermission(All_GroupID_Show_TruckBooking)) || (IsClientAdmin() && ValidateUserPermission(Admin_GroupID_Show_TruckBooking))) { ?>
			<li><a href="truck_booking.php">Truck Booking</a></li>
		<?php } ?>
		<?php if((IsAdmin() && ValidateUserPermission(All_GroupID_Show_DeliveryBooking)) || (IsClientAdmin() && ValidateUserPermission(Admin_GroupID_Show_DeliveryBooking))) { ?>
			<li><a href="delivery_booking.php">Delivery Booking</a></li>
		<?php } ?>
		<?php if(IsAdmin() || IsClientAdmin()) { ?>
		<li><a href="javascript:TracknTrace('TrackAndTrace/index.php','Shipments Track and Trace')">Track and Trace</a></li>
		<?php } ?>
			<!--<li><a href="testpage.php">test page</a></li>
			<li><a href="util_itempricing_script.php">util</a></li>-->
		<?php if(IsAdmin() || IsClientAdmin() ) { ?>
		<?php //<li><a href="wmsprealert.php" target="_blank">Pre-Alert for WMS</a></li> ?>
		<li><a href="pre-alert.php?showtab=3">Distribution Pre-alert</a></li>
		<?php } ?>
		<?php if((IsAdmin() && ValidateUserPermission(All_GroupID_Show_LessVolumeForApproval)) || (IsClientAdmin() && ValidateUserPermission(Admin_GroupID_Show_LessVolumeForApproval))) { ?>
			<li><a href="less_volume_for_approval.php">Less Volume for Approval</a></li>
		<?php } ?>
		<?php if((IsAdmin()) || (IsClientAdmin()) || (IsCourierDriver()) || (IsCourierContractor()) || (IsCourierEmployee())) {?>
		<li><a href="manage_picklist.php?showtab=1">Pick List</a></li>
		<li><a href="pre-alert_management.php?showtab=1">Pre-alert Management</a></li>
		<?php } ?>
		<?php if(IsAdmin() || IsClientAdmin() ) { ?>
		<li><a href="manage_parent_waybill.php?showtab=1">Create Parent Waybill</a></li>
		<li><a href="liquidation.php?showtab=1">DR/CSM Expense Liquidation</a></li>
		<?php } ?>
		<?php if(IsAdmin() || IsClientAdmin()) { ?>
		<li><a href="csm_sp_cash_advance.php">SP Cash Advance</a></li>
		<?php } ?>
		<?php if(IsAdmin() || IsClientAdmin()) { ?>
		<li><a href="csm_sp_liquidation.php">SP Expense Liquidation</a></li>
		<?php } ?>
		<?php if(IsAdmin() || IsClientAdmin()) { ?>
		<li><a href="csm_sp_compensation.php">SP Compensation</a></li>
		<?php } ?>
		<?php if(IsAdmin() || IsClientAdmin()) { ?>
		<li><a href="shipment_cd_cashadvance.php">Courier / Driver Cash Advance</a></li>
		<?php } ?>
		<?php if(IsAdmin() || IsClientAdmin()) { ?>
		<li><a href="shipment_cd_liquidation.php">Courier / Driver Liquidation</a></li>
		<?php } ?>
		<?php if(IsSP() || IsCourier() || IsSPCourier()) { ?>
		<!--<li><a href="shipment_cd_liquidation.php">Courier / Driver Liquidation</a></li>-->
		<li><a href="shipment_cd_liquidation_bs.php">Courier / Driver Liquidation</a></li>
		<?php } ?>
		<?php if(IsAdmin() || IsClientAdmin()) { ?>
		<li><a href="manage_parent_waybill.php?showtab=1">Consolidate Waybills with Same Consignee</a></li>
		<?php //<li><a href="dtl.php?showtab=1">DTL</a></li> ?>
		<li><a href="javascript:ShowSmsInbox('tab_wid_prealert.php?acc_code=<?php echo LWLI_CLIENT_ONLINE ?>&facid=<?php echo WMS_MAIN_HUB ?>','WMS Outbound/Bin Transfer  Pre-alert (<u style=\'color:#ff0000\'><?php echo LWLI_CLIENT_ONLINE ?> <?php echo WMS_MAIN_HUB ?> Hub</u>)'<?php echo ((LWLI_CLIENT_ONLINE=='LWLI') ? ',true' : '') ?>)">WMS Outbound/Bin Transfer Pre-alert</a></li>
		<li><a href="tab_purchaseorder.php?show=2">Purchase Order</a></li>
		<?php } ?>
		<?php if(IsAdmin() || IsClientAdmin()) { ?>
		<li><a href="manage_csm.php?showtab=0">Waybill Consolidation</a></li>
		<li><a href="manage_mastercsm.php?showtab=5">CSM Consolidation</a></li>
		<li><a href="delivery_dispatch.php?showtab=1">Gatepass/Shipment Dispatch</a></li>
		<?php } ?>
	
		<?php if(IsAdmin() || IsClientAdmin()) { ?>
		<li><a href="client_box_supply.php?showtab=2">Client Box Supply</a></li> 
		<?php }   ?>
		</ul>
	</li>
	<?php } else if((IsSP() && ValidateUserPermission(SP_GroupID_Delivery_Expense_Liquidation))) { ?>
		<li><a href="csm_sp_liquidation.php">Delivery Expense Liquidation</a></li>
		<?php
	    } elseif((IsCourierDriver()) || (IsCourierEmployee())) { ?>
		<li><a href="csm_sp_liquidation.php">Delivery Expense Liquidation</a></li>
		<?php } ?>
		<?php if((IsSP() &&  ValidateUserPermission(SP_GroupID_mycourier))){ ?>
		<li><a href="manage_sp.php?showtab=4">My Courier</a></li>
		<?php } ?>
					<?php
					if((IsAdmin()) || ($_SESSION['userid']!='65' && (ValidateUserPermission(Client_GroupID_Waybill_List) || ValidateUserPermission(Client_GroupID_LDS_tracking) || ValidateUserPermission(Client_GroupID_Trip_Monitoring) || ValidateUserPermission(Client_GroupID_Intransit) || ValidateUserPermission(Client_GroupID_Show_Delivered_Reports) || ValidateUserPermission(Admin_GroupID_Waybill_List) || ValidateUserPermission(Admin_GroupID_Trip_Monitoring) || ValidateUserPermission(Admin_GroupID_InTransit) || ValidateUserPermission(Admin_GroupID_Delivered) ) || (IsSP() && ValidateUserPermission(SP_GroupID_InTransit)) || IsCourierDriver() || IsCourierEmployee() || IsCourierContractor())) { 
					?>
						<li class="dropdown">
						<a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button"  aria-expanded="false">Reports <span class="caret"></span></a>
						<ul class="dropdown-menu">
							<?php if($_SESSION['AccountCode']=='HPI' || $_SESSION['AccountCode']=='SPI' || $_SESSION['AccountCode']=='MNC' || $_SESSION['AccountCode']=='LWLITEST') { ?>
							<li><a href="waybills_barcoding.php" target="_blank">Barcode of Items/Units</a></li>
						<?php } ?>
						<?php if(!IsClient()){ ?>
						<li><a href="javascript:ListofRejectedPOD('list_of_rejectedpod.php','List of Rejected POD')">List of Rejected POD's</a></li>
						<?php } ?>
							<?php if((IsAdmin() || IsClientAdmin() || (IsClient() && ValidateUserPermission(Client_GroupID_LDS_tracking)) )){ ?>
							 <li><a href="lds_tracking.php">Shipments Tracking of Delivery</a></li> 
							<?php } ?>
							<?php
							if(IsAdmin() || IsClientAdmin()){
							?>
							<!-- <li><a href="profitability_report.php">Profitability Report</a></li> -->
							<li><a href="collection_detail_comparison.php">Fuel Detail Comparison</a></li>
							<li><a href="manage_billing_report.php?showtab=1">Billing Report</a></li>
							<li><a href="summaryofbilling_report.php">Summary of Billing</a></li>
							<?php } ?>
							<?php if((IsClientAdmin() && ValidateUserPermission(Admin_GroupID_Waybill_List)) ||
								 (IsAdmin() && ValidateUserPermission(All_GroupID_Waybill_List))) { ?>
								<li><a href="waybill_list.php">Waybill List</a></li>
							<?php } ?>
							
							<?php if((IsClientAdmin() && ValidateUserPermission(Admin_GroupID_Trip_Monitoring)) || (IsAdmin() && ValidateUserPermission(All_GroupID_Trip_Monitoring))) { ?>
								 <li><a href="trip_monitoring.php">Trip Monitoring</a></li>
							<?php } ?>
							
							<?php
							$queryFilters = '';
							if(isset($_GET['acctcodefilter']) && trim($_GET['acctcodefilter'])<>'' && LWLI_CLIENT_ONLINE==ADMIN_USERS_COMPANY) {
								$queryFilters .= " AND UPPER(TRIM(ts.account))=UPPER(TRIM('".FieldDBInput($_GET['acctcodefilter'])."'))";
							} elseif(LWLI_CLIENT_ONLINE<>ADMIN_USERS_COMPANY && trim(LWLI_CLIENT_ONLINE)<>'') {
								$queryFilters .= " AND UPPER(TRIM(ts.account))=UPPER(TRIM('".FieldDBInput(LWLI_CLIENT_ONLINE)."'))";
							}
								$query=doQuery("SELECT count(DISTINCT wtfs.shipment_magic) as noprealert 
												FROM web_tf_shipments wtfs
												LEFT JOIN tf_shipments ts ON wtfs.shipment_magic=ts.billed 
												LEFT JOIN web_mf_accounts wm ON wm.account=wtfs.account ".
												"WHERE (wm.status=0) AND (trim(ts.account)<>'')
										         AND (ts.billed IS NOT NULL)
										         AND wtfs.shipment_movement_status=".PRE_ALERTED_STATUS_ID." ".$queryFilters ." ".QueryFilterByDepartment('departmentID','wtfs','AND')." ");
								$row=mysql_fetch_array($query);
							?>
							<?php if((IsClient() && ValidateUserPermission(Client_GroupID_pending_prealert)) ||
								 (IsClientAdmin() && ValidateUserPermission(Admin_GroupID_Pre_Alert)) || 
								 (IsAdmin() && ValidateUserPermission(All_GroupID_Pre_Alert))) { ?>
								<li>
									<a href="prealert_report.php?showtab=1">Pending Pre-alert
										<?php if($row['noprealert']==0) { ?>
											<span class="badge notired"></span>
										<?php } else { ?>
											<span class="badge notired"><?php echo $row['noprealert'] ?></span>
										<?php } ?>
									</a>
								</li>
							<?php } ?>
							<?php //if(IsAdmin() || IsClientAdmin() && ($_SESSION['AccountCode']=="SPI" || $_SESSION['AccountCode']=="HPI" || $_SESSION['AccountCode']=="MNC")) { ?>
							<!---<li><a href="receiving_report.php">Receiving Report</a></li>-->
							<?php //} ?>
							
							<?php if((IsClientAdmin() && ValidateUserPermission(Admin_GroupID_eWallet_SOA)) || (IsAdmin() && ValidateUserPermission(All_GroupID_eWallet_SOA)) || (IsSP() && ValidateUserPermission(SP_GroupID_soa)) ) { ?>
							<li><a href="sp_soa.php">eWallet Statement of Account</a></li>
							<?php } ?>	

							<?php if((IsClientAdmin() && ValidateUserPermission(Admin_GroupID_InTransit)) || 
								 	 (IsAdmin() && ValidateUserPermission(All_GroupID_InTransit)) ||
								 	 (IsSP() && ValidateUserPermission(SP_GroupID_InTransit))  || (IsCourierDriver()) || (IsCourierContractor()) || (IsCourierEmployee())) { ?>
							<li><a href="summary_report.php">Intransit</a></li>
							<?php } ?>
							
							<?php if((IsAdmin() && ValidateUserPermission(All_GroupID_Show_Shipment_Profitability)) || (IsClientAdmin() && ValidateUserPermission(Admin_GroupID_Show_Shipment_Profitability))){ ?>
							<li><a href="shipment_profitability.php">Shipment Profitabilty Summary</a></li>
							<?php } ?>

							<?php if(IsAdmin() || IsClientAdmin()){ ?>
							<li><a href="cad_dm.php">Courier and Driver Debit Memo</a></li>
							<li><a href="cad_ca.php">Courier and Driver Cash Advances</a></li>
							<li><a href="cad_fdrpu.php">Dispatch and Pick-Up Monitoring</a></li>
							<li><a href="cad_drpube.php">Dispatch and Pick-Up Budget Expense</a></li>
							<li><a href="sp_for_compensation_summary.php">SP For Compensation Summary</a></li>
							<?php } ?>

							

							<?php if((IsClientAdmin() && ValidateUserPermission(Admin_GroupID_StatusReport)) || 
								 (IsAdmin() && ValidateUserPermission(All_GroupID_StatusReport))) { ?>
							<li><a href="status_report.php">Status Report</a></li>
							<li><a href="wb_return_whse.php?showtab=2">List of Waybills Return to Warehouse</a></li>
							<li><a href="csm_return_whse.php?showtab=2">List of CSM Return to Warehouse</a></li>
							<?php } ?>							
							<?php if((IsClientAdmin() && ValidateUserPermission(Admin_GroupID_Delivered)) || 
								 (IsAdmin() && ValidateUserPermission(All_GroupID_Delivered)) ||
								 (IsSP()) || (IsCourierDriver()) || (IsCourierEmployee()) || (IsCourierContractor()) || 
								 (IsClient() && ValidateUserPermission(Client_GroupID_Show_Delivered_Reports))  || (IsCourierDriver()) || (IsCourierContractor()) || (IsCourierEmployee()) ) { ?>
							<li><a href="delivered_report.php">Delivered</a></li>
							<?php } ?>
							<?php if((IsClientAdmin())){?>
							<!-- <li><a href="shipments_filtered.php">Shipments filtered</a></li> -->
							<?php } ?>
							<?php if((IsClientAdmin() && ValidateUserPermission(Admin_GroupID_Exception_Report)) ||
								 (IsAdmin() && ValidateUserPermission(All_GroupID_Exception_Report)) ||
								 (IsSP())  || (IsCourierDriver()) || (IsCourierEmployee()) || (IsCourierContractor())) { ?>
							<li><a href="exception_report.php">Exception</a></li>
							<li><a href="kpi_report.php">Delivery KPI</a></li>
							<li><a href="kpi_report_appusage.php">Delivery KPI for Realtime App Usage</a></li>
							<?php
							}
							?>
							<?php if(IsAdmin() || IsClientAdmin()) { ?>
							<li><a href="gps_report.php?showtab=1">Truck Trip Monitoring</a></li>
							<?php } ?>
							<?php if(IsAdmin() || IsClientAdmin()) { ?>
							<li><a href="truck_trip_monitoring_summary.php?showtab=1">Truck Trip Monitoring Summary</a></li>
							<?php } ?>
							<?php if(IsAdmin() || IsClientAdmin()) { ?>
							<li><a href="detailed_ingroup_report.php">CSM & DR/Gatepass Group Report</a></li>
							<?php } ?>
						</ul>
					</li>
				<?php } ?>
					<?php if(IsClient() && ValidateUserPermission(Client_GroupID_Create_Pre_Alert)) { ?>
						<li><a href="pre-alert.php?showtab=3">Pre-alert</a></li>
					<?php } elseif(IsClient() && ValidateUserPermission(Client_GroupID_Pre_Alert_List)) { ?>
						<?php if($_SESSION['AccountCode']=='PRIMER') { ?>
						<li><a href="pre-alert.php?showtab=1">Pre-alert</a></li>
						<?php } else { ?>	
							<li><a href="pre-alert.php?showtab=2">Pre-alert</a></li>
					<?php } }?>	
				
					
					
					<?php if(IsAdmin() || IsClientAdmin()) { ?>
						<li class="dropdown">
							<a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button"  aria-expanded="false">Billing <span class="caret"></span></a>
							<ul class="dropdown-menu">
							<li><a href="tab_billing.php?show=2">TMS Billing</a></li>
							<li><a href="wms_billing.php?show=1">WMS Billing</a></li>
							<li><a href="manage_invoice_series.php">Invoice Series</a></li>
							<?php 
							if(LWLI_CLIENT_ONLINE=='MNC') {
							?>
							<li><a href="javascript:ViewUploadRates('view_upload_rates.php','View/Upload Rates')">View/Upload Rates</a></li>
							<?php } ?>
							<!--
							<li><a href="manage_billingtype.php">Billing Types</a></li>
							<li><a href="#">Brokerage Billing</a></li>
							-->
							</ul>
						</li>
					<?php } ?>
							
				
				<?php 
					$paramclient = '';
					if(isset($_GET['goto_previous']) && $_GET['goto_previous']==1) {
					  $paramclient = '&basefile='.basename($_SERVER['PHP_SELF']).'&baseparam='.urlencode($_SERVER['QUERY_STRING']);
					}
					?>
					
					<?php if(IsAdmin() || IsClientAdmin() || IsDepartment()) { ?>
					<li class="dropdown">
					<a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button"  aria-expanded="false">Clients<span class="caret"></span></a>
					<ul class="dropdown-menu">
					  <li><a href="main.php?op=0&acctcode=<?php echo ADMIN_USERS_COMPANY ?>">ALL CLIENTS</a></li>
					  <?php
					  $query=doQuery("SELECT mfa.name,
									         mfa.account,
											 mfa.hubcode,
											 wmfa.departments 
									  FROM mf_accounts mfa LEFT JOIN web_mf_accounts wmfa ON wmfa.account=mfa.account  WHERE mfa.withactivetransactions=1 GROUP BY mfa.name ORDER BY mfa.name");
					  while($row=mysql_fetch_array($query)){
						if(!ValidateUserMultiDepartment($row['departments']))
						   CONTINUE; 
					  ?>
					  <li><a href="main.php?op=1&acctcode=<?php echo $row['account'].$paramclient  ?>"><?php echo $row['name'] ?></a></li>
					  <?php
					  }
					  ?>
					  <?php //if($_SESSION['userid']=='11') { ?>
					  <li><a href="manage_client.php?showtab=2&acctcode=LWLI&show=all" data-toggle="tooltip" data-placement="top"><i class="icon-arrow-right icon-medium"></i>&nbsp;SHOW ALL CLIENTS</a></li>
					  <li><a href="manage_client.php?showtab=1" data-toggle="tooltip" data-placement="top"><i class="icon-plus icon-medium"></i>&nbsp;ADD CLIENT</a></li>
					
					<?php //} ?>
					</ul>
					</li>
					<?php } ?>

					<?php if($_SESSION['userid']!='65') { ?>
					<!----shipper---->
					<?php if(IsAdmin() || IsClientAdmin() || (IsClient() && ValidateUserPermission(Client_GroupID_Shipper_Registration))) { ?>	
					<!-- <li><a href="shippers_contact.php?showtab=1">Shipper</a></li> -->
					<?php } elseif(IsClient() && ValidateUserPermission(Client_GroupID_Shipper_List)) { ?>
						<!-- <li><a href="shippers_contact.php?showtab=2">Shipper</a></li> -->
					<?php } ?>
					<!----consignee---->
					<?php if(IsAdmin() || IsClientAdmin() || (IsClient() && ValidateUserPermission(Client_GroupID_Consignee_Registration))) { ?>
					<!-- <li><a href="create_contact.php?showtab=1">Consignee</a></li> -->
					<?php } elseif(IsClient() && ValidateUserPermission(Client_GroupID_Consignee_List)) {  ?>
						<!-- <li><a href="create_contact.php?showtab=2">Consignee</a></li> -->
					<?php } } ?>

					<?php if(IsAdmin() || IsClientAdmin()) 
					{ 
						echo '<li><a href="editpage.php?showtab=1">Shipments</a></li>';
					} elseif(IsSP()) {
						echo '<li><a href="main.php?showhome=1">My Dispatch & E-Wallet</a></li>';
					}
					?>
				    <li><a href="shipments.php">My Shipments</a></li>
					<?php if( (IsAdmin() && ValidateUserPermission(All_GroupID_Show_WMS)) || (IsClientAdmin() && ValidateUserPermission(Admin_GroupID_Show_WMS)) || (IsClient() && ValidateUserPermission(Client_GroupID_Show_WMS)))
					{
						echo '<li><a title="Warehouse Management System" href="wms.php">WMS</a></li>';
					}
					?>

					<?php if( (IsAdmin() && ValidateUserPermission(All_GroupID_Show_HRIS)) || (IsClientAdmin() && ValidateUserPermission(Admin_GroupID_Show_HRIS)) )
					{
						echo '<li><a title="Human Resource Information System" href="hris.php">HRIS</a></li>';
					}
					?>

					<?php if( (IsAdmin() && ValidateUserPermission(All_GroupID_Show_CRM)) || (IsClientAdmin() && ValidateUserPermission(Admin_GroupID_Show_CRM)) )
					{
						echo '<li><a title="Customer Relation Management System" href="crm.php">CRM</a></li>';
					}
					?>
					
					
						<!-- <li title="Delivery Expense,Compensation,Liquidation & Warehouse Managment System"><a title="Delivery Expense,Compensation,Liquidation & Warehouse Managment System" href="wms.php">MIS & WMS</a></li> -->


						<?php if(IsAdmin()){ ?>
						<li><a href="sp_ewallet.php" target="_self">E-Wallet</a></li>
						<?php }
						$IsPODLinkShown = false;
						if((IsCourierDriver()) || (IsCourierEmployee()) || (IsCourierContractor()) || (IsSPCourier())) {
						$IsPODLinkShown = true;
						?>
					 
					 <li class="dropdown">
						<a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button"  aria-expanded="false"><i class="icon icon-calendar icon-large"></i>Update DR<span class="caret"></span></a>
					 <ul class="dropdown-menu">
					 <li><a title="DR/Gatepass Acceptance Update" href="javascript:TracknTrace('modal_gatepass.php?noheaderlogo=1','<i class=\'icon icon-calendar\'></i>&nbsp;(DR/Gatepass) Received Confirmation Update');">DR/Gatepass Received Update</a></li>
					 <li><a title="DR/Gatepass Acceptance Update" href="javascript:TracknTrace('modal_gatepass.php?noheaderlogo=1&up=img','<i class=\'icon icon-calendar\'></i>&nbsp;(DR/Gatepass) Upload Attachment Update');">DR/Gatepass Upload DR Image</a></li>
					 </ul>
					</li>
					 
					<li class="dropdown">
					    <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button"  aria-expanded="false"><i class="icon icon-calendar icon-large"></i>Update CSM<span class="caret"></span></a>
						<ul class="dropdown-menu">
					    <li><a title="Update Received by Consolidator (Bus, Air, Shipping Lines)" href="javascript:ShowUpdateRAG('summary_report.php?acceptno=2&showtab=9&show_dispatched=1&showoptions=1&noheaderlogo=1','<i class=\'icon icon-calendar\'></i>&nbsp;Update Received by Consolidator (Bus, Air, Shipping Lines)');">CSM Received by Consolidator</a></li>
					    <li><a title="Update Received at Gateway by Service Partner" href="javascript:ShowUpdateRAG('summary_report.php?acceptno=0&showtab=9&show_shipping=1&showoptions=2&noheaderlogo=1','<i class=\'icon icon-calendar\'></i>&nbsp;Update Received at Gateway by Service Partner');">CSM Received by Sevice Partner</a></li>
					    <?php /*
						<li><a title="Transfer Waybill from Direct Delivery to Service Partner" href="javascript:ShowUpdateRAG('modal_transferwaybills.php?noheaderlogo=1','<i class=\'icon icon-exchange\'></i>&nbsp;Transfer Undelivered Waybills from Direct Delivery to Service Partner');">Transfer Undelivered Waybills to Sevice Partner</a></li>
					    */ ?>
					    </ul>
					</li>
						<li><a title="Update Shipment POD Status (Direct Delivery to Consignee)" href="javascript:ShowUploadPOD('upload_pod.php','<i class=\'icon icon-upload\'></i>&nbsp;<i class=\'icon icon-picture\'></i>&nbsp;Update Shipment POD Status <br>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(Direct Delivery to Consignee)')"><i class="icon icon-upload icon-large"></i>Upload POD</a></li>
						<li><a href="javascript:ShowReturnWaybill('wb_return_whse.php?showtab=1','&nbsp;Update Receiving of Waybills Return to Warehouse')"><i class="icon icon-edit icon-large"></i>Waybills Return to Warehouse</a></li>
					<?php } elseif((IsAdmin()) || (IsClientAdmin() && ValidateUserPermission(Admin_GroupID_WebPODupload))) { ?>
					<li class="dropdown">
					    <a href="#" class="dropdown-toggle" data-toggle="dropdown" role="button"  aria-expanded="false">CSM / POD / RETURN WAYBILLS<span class="caret"></span></a>
						<ul class="dropdown-menu">
					    <li><a href="javascript:ShowUpdateRAG('summary_report.php?showtab=9&show_shipping=1&noheaderlogo=1','<i class=\'icon icon-calendar\'></i>&nbsp;UPDATE RECEIVED AT GATEWAY');"><i class="icon icon-calendar icon-large"></i>Update CSM <?php echo (IsSP()) ? 'RAG' : '' ?></a></li>
					    <li><a href="javascript:ShowUploadPOD('upload_pod.php','<i class=\'icon icon-upload\'></i>&nbsp;<i class=\'icon icon-picture\'></i>&nbsp;Update Shipment POD Status')"><i class="icon icon-upload icon-large"></i>Upload POD</a></li>
					    <li><a href="javascript:ShowReturnWaybill('wb_return_whse.php?showtab=1','<i class=\'icon-tasks\'></i>&nbsp;Update Receiving of Waybill Return to Warehouse')"><i class="icon-tasks"></i>Waybills Return to Warehouse</a></li>
					    <li><a href="javascript:ShowReturnCSM('csm_return_whse.php?showtab=1','<i class=\'icon-tasks\'></i>&nbsp;Update Receiving of CSM Return to Warehouse')"><i class="icon-tasks"></i>CSM Return to Warehouse</a></li>
						<li><a href="javascript:ShowCSMScanPackUploading('csm_scanpack_uploading.php?showtab=1','<i class=\'icon-tasks\'></i>&nbsp;Uploading of Waybill Scan & Pack')"><i class="icon-tasks"></i>CSM Scan & Pack Uploading</a></li>
					    <li><a onclick="updatedt('modal_update_dispatch.php?action=1','Update Gate Pass')"><i class="icon-time"></i>Dispatch & Return</a></li>
					   <!--  <li><a href="wb_return_whse.php?showtab=1"><i class="icon icon-edit icon-large"></i>Waybills Return to Warehouse</a></li> -->
						</ul>
					</li>
					<?php } ?>
					<?php if(IsSP()) { ?>
					<li><a href="javascript:ShowUpdateRAG('summary_report.php?showtab=9&show_shipping=1&noheaderlogo=1','<i class=\'icon icon-calendar\'></i>&nbsp;UPDATE RECEIVED AT GATEWAY');"><i class="icon icon-calendar icon-large"></i>Update CSM <?php echo (IsSP()) ? 'RAG' : '' ?></a></li>
					<?php if(!$IsPODLinkShown) { ?>
					<li><a href="javascript:ShowUploadPOD('upload_pod.php','<i class=\'icon icon-upload\'></i>&nbsp;<i class=\'icon icon-picture\'></i>&nbsp;Update Shipment POD Status')"><i class="icon icon-upload icon-large"></i>Upload POD</a></li>
					<?php }
					} ?>
					
					
					<?php if(IsSP()  || IsAdmin() || IsClientAdmin() || IsCourierDriver() || IsCourierEmployee()) { ?>
					
					<li><a href="javascript:TracknTrace('add_remarksv2.php','<?php echo IsSP() ? 'CSM / Waybills' : 'DR / CSM / Waybills & Pre-alert Shipments' ?> Remarking')">Remarks</a></li>
					<?php } ?>
					<?php if(IsClient() && ValidateUserPermission(Client_GroupID_Mybilling)) { ?>
					<li><a href="javascript:ShowSmsInbox('tab_soa.php','Statement of Account')">My Billing</a></li>
					<?php } ?>
					<?php /*if(IsClient() && ValidateUserPermission(Client_GroupID_Mybilling)){ ?>
					<li><a href="client_ewallet.php">E-Wallet</a></li>
					<?php } */?>
					<?php /*if(IsClient() && ValidateUserPermission(Client_GroupID_Mybilling)){ ?>
					<li><a href="client_soa.php">Statement of Account</a></li>
					<?php } */?>
				    <?php if(IsClient()){ ?>
					<li><a href="javascript:TracknTrace('TrackAndTrace/index.php','Shipments Track and Trace')">Track and Trace</a></li>
					<?php } ?>
					
					<?php if(IsSP()  || IsAdmin() || IsClientAdmin() || IsCourierDriver() || IsCourierEmployee()) { ?>
					<li><a href="how_to.php" target="_self">Tutorial Videos</a></li>
					<?php } ?>
					<?php if((IsSP() && ValidateUserPermission(SP_GroupID_ewallet)) || IsCourierDriver() || IsCourierEmployee()) { ?>
					<li><a href="sp_ewallet.php" target="_self">E-Wallet</a></li>
					<?php } ?>
					<?php if((IsSP() || IsCourierDriver() || IsCourierEmployee() || (IsCourierContractor()) || IsClient()) && isset($_SESSION['CurrentAdminLoggedIn']) && $_SESSION['CurrentAdminLoggedIn'] > 0) { ?>
					<li><a href="/?viewCAcct=1" target="_self">My Account</a></li>
					<?php } ?>
					
				</ul>
			</div><!--/.nav-collapse -->
		</div>
		
		<?php 
			$userid=$_SESSION['userid'];
			$query=doQuery("SELECT * FROM web_webusers WHERE user='".(int)$userid."'");
			$row=mysql_fetch_array($query);
			$id=$row['user'];
		?>
		<div id="user_main_home_logout">
		<?php  if(IsSP()) {
			
  $uweb = doQuery("SELECT lastlogin FROM web_webusers WHERE accountsp='".FieldDBInput($_SESSION['AccountSP'])."' ORDER BY lastlogin DESC limit 1");
  $lg = 'NONE';
  if(mysql_num_rows($uweb) > 0) {
    $uinfo = mysql_fetch_array($uweb);
    $lg = (($uinfo['lastlogin']!='') ? FormatDate($uinfo['lastlogin'],5) : " NONE ");
  }
  echo "<span id=\"sp_login_history\">&nbsp;&nbsp;&nbsp;(<span title='".FieldDBInput($_SESSION['AccountSP'])."Las MyBiz Web App Login: ".$lg."'>Web App: ".$lg."</span> ";
  
 
  $uweb = doQuery("SELECT waybillTaskUploadDateTime FROM app_waybill_task_active a 
                  LEFT JOIN web_webusers u on u.user=a.waybillTaskUploadBy WHERE u.accountsp='".FieldDBInput($_SESSION['AccountSP'])."' 
                  ORDER BY waybillTaskUploadDateTime DESC limit 1");
  $lg = 'NONE';
 
  if(mysql_num_rows($uweb) > 0) {
    $uinfo = mysql_fetch_array($uweb);
    $lg = (($uinfo['waybillTaskUploadDateTime']!='') ? FormatDate($uinfo['waybillTaskUploadDateTime'],5) : " NONE ");
  }
  echo "&nbsp;&nbsp;<span title='".FieldDBInput($_SESSION['AccountSP'])." Last Mobile App: ".$lg."'>Mobile App: ".$lg."</span>)&nbsp;</span>";
  
		 }

	 echo '<a href="change_password.php?fromlist=1&user='.urlencode(EncryptKeyword($row['user'])).'" title="Change Password">'.$row['firstname']." ".$row['lastname'];?>&nbsp;&nbsp;</a>
	 <a href="logout.php" target="_top">Logout</a>
	 </div>
			
</nav>

<script>
	function updatedt(eurl,etitle,size='sm'){
		var options = {
			  url: eurl,
			  size: size
		   	};
			eModal.iframe(options,etitle);
	}

	function TracknTrace(eurl,etitle) {
		var options = {
			url: eurl,
			size: 'lg'
		};
		eModal.iframe(options,etitle);
	}

	function ViewUploadRates(eurl,etitle) {
		var options = {
			url: eurl,
			size: 'lg'
		};
		eModal.iframe(options,etitle);
	}

	function ListofRejectedPOD(eurl,etitle) {
		var options = {
			url: eurl,
			size: 'lg'
		};
		eModal.iframe(options,etitle);
	}


	$("#searchwaybillcsm").submit(function(){
		var searchID=$('#searchFldType').val();
		var searchno=$('#waybillcsmsearch').val();
		if(searchno.trim()!='') {
			if(searchID==1){
				window.location='editpage.php?showtab=1&waybillno='+searchno;
			} else if(searchID==3) {
				window.location='editpage.php?showtab=1&tripno='+searchno;
			} else if(searchID==4) {
				window.location='editpage.php?showtab=1&orderno='+searchno;
			} else if(searchID==5) {
				<?php if(IsClient()) { ?>
				window.location='tab_soa.php?billinginvnum='+searchno;
				<?php } else { ?>
				window.location='tab_billing.php?show=2&billinginvnum='+searchno;
				<?php } ?>
			} else if(searchID==6) {
				window.location='delivery_dispatch.php?showtab=8&drno='+searchno;
			} else if(searchID==7) {
				window.location='editpage.php?showtab=1&csmno='+searchno;
			} else if(searchID==8) {
				window.location='editpage.php?showtab=8&drno='+searchno;
			} else if(searchID==9) { 
				window.location='manage_mastercsm.php?showtab=8&csmno='+searchno;
			} else {
				window.location='manage_csm.php?showtab=4&csmno='+searchno;
			}
		} else {
		  $('#waybillcsmsearch').focus();
		}
		return false;
	});	
function ShowUploadPOD(eurl,etitle) {
		var options = {
			url: eurl,
			size: 'pod'
		};
		eModal.iframe(options,etitle);
	}

	function ShowReturnWaybill(eurl,etitle) {
		var options = {
			url: eurl,
			size: 'pod'
		};
		eModal.iframe(options,etitle);
	}

	function ShowReturnCSM(eurl,etitle) {
		var options = {
			url: eurl,
			size: 'pod'
		};
		eModal.iframe(options,etitle);
	}
	function ShowCSMScanPackUploading(eurl,etitle) {
		var options = {
			url: eurl,
			size: 'pod'
		};
		eModal.iframe(options,etitle);
	}

function ShowUpdateRAG(eurl,etitle) {
		var options = {
			url: eurl,
			size: 'xl'
		};
		eModal.iframe(options,etitle);
	}	

function ShowWaybillBarcode(eurl,etitle) {
		var options = {
			url: eurl,
			size: 'xl'
		};
		eModal.iframe(options,etitle);			
	}
function SOAeWallet(eurl,etitle) {
	var options = {
			url: eurl,
			size: 'xl'
		};
	eModal.iframe(options,etitle);			
}	
function ShowSmsInbox(eurl,etitle,clrequired=false) {
	     if(clrequired) {
			eModal.alert('Please select client to continue WMS pre-alert management.','Client is Required');
		 } else {
		    eModal.iframe(eurl,etitle);
		 }
	}

</script>
<style>
	  .dropdown-menu {margin-top: 4px !important;}
	  .navmenu-ico { font-size: 18px !important;}
	  .navsubmenu-ico { font-size: 16px !important;padding-top:1px;}
	  #waybillcsmsearch {max-width:98px;}
	  .apistatus .row div input,.apistatus .row div select,.apistatus .row div button {min-height:25px;}
	  <?php if($_SERVER['PHP_SELF']=='/wms.php') { ?>
	  .apistatus .row {margin-top:-16px; width:235px;}
	  <?php } else { ?>
	  .apistatus .row {margin-top:-8px; width:235px;}
	  <?php } ?>
	  @media only screen and (max-width: 1024px) {
	 .dropdown-menu {margin-top:10px; border-top: 1px solid #ffff00 !important;}
	 .navbar-collapse {margin-top:50px;}
	 .navbar-nav {margin-left:0 !important; margin-top:10px !important; margin-right:-10px !important;}
	 .apistatus {float:left;margin-left:0;display:block;height:70px;}
	 <?php if($_SERVER['PHP_SELF']=='/wms.php') { ?>
	 .apistatus .row {float:left;margin-right:-10px;margin-top: 35px; width:300px;}
	 <?php } else { ?>
	 .apistatus .row {float:left;margin-right:-10px;margin-top: 45px; width:300px;}
	 <?php } ?>
	 .apistatus .row div {display: inline-block; margin:0; padding:0;}
	 .apistatus .row div input,.apistatus .row div select,.apistatus .row div button {font-size: 11px; max-width:80px;max-height:22px;}
	 .apistatus .row div select { max-width:110px;max-height:22px;}
	 .apistatus #statustext {font-size: 8px;float: right;}
	  #searchwaybillcsm{margin-right:0px;margin-top: 0;float: right;}
 	  #statustext{display: none;}
 	  #client_logo{padding-top:5px;}
	}
	@media only screen and (max-width: 380px) {
		.apistatus .row {margin-right:-90px;}
		#client_logo{margin-top:20px;}
		#statustext{ display: none;}
		#waybillcsmsearch {max-width:80px;}
		#client_logo{padding-top:5px;}
		.sp_wallet_soa{ padding:0;margin:0 !important}
		#sp_login_history{display: none;}
		#sp_name_top_menu{display:block; padding-top:5px;}
	}
</style>
