<?php
$_GET['donotshowheader'] = 1;
include('header.php');
include('session.php');

// Base query filters
$queryFilters = '';
if(LWLI_CLIENT_ONLINE<>ADMIN_USERS_COMPANY && trim(LWLI_CLIENT_ONLINE)<>'') {
    $queryFilters .= " AND UPPER(TRIM(tfs.account))=UPPER(TRIM('".FieldDBInput(LWLI_CLIENT_ONLINE)."'))";
}

if(IsSP()) {
   $queryFilters .= " AND (EXISTS (SELECT * FROM tf_consolidate tchk WHERE tchk.csmno=tfs.csmno AND tchk.sp_id='".(int)$_SESSION['sp_id']."')) ";
}

if(IsCourierContractor()) {
   $queryFilters .= " AND (EXISTS (SELECT * FROM tf_dispatch tchk WHERE tchk.drno=tfs.drno AND tchk.sfc_id='".(int)$_SESSION['CourierID']."')) ";
}

if(IsCourierDriver() || IsCourierEmployee()) {
   $queryFilters .= " AND (EXISTS (SELECT * FROM tf_dispatch tchk WHERE tchk.drno=tfs.drno AND tchk.sfc_id='".(int)$_SESSION['sp_id']."')) ";
}

// Get status filter from POST request
$statusFilter = '';
$leftJoin = "";
$selectQuery = " tfd.destination as destination, tfd.drtype as mode_of_delivery, tfd.release_status as release_status, tfd.updateacceptedby, tfc.spname ";
$caseSelect = "";

if(isset($_POST['status'])) {
    switch($_POST['status']) {
        case 'direct_delivery':
            $leftJoin = " LEFT JOIN tf_dispatch tfd ON tfd.drno=tfs.drno";
            $leftJoin .= " LEFT JOIN tf_consolidate tfc ON tfc.csmno=tfs.csmno";
            $leftJoin .= " LEFT JOIN web_mf_partners sp ON sp.sfc_id=tfd.sfc_id";
            
            // Base filter for direct delivery shipments
            $statusFilter = " AND wtfs.shipment_movement_status = ".SHIPMENT_DISPATCHED_STATUS_ID;
            $statusFilter .= " AND tfd.drtype = 'DIRECT DELIVERY TO CONSIGNEE'";
            
            // Add specific status filtering if requested
            if(isset($_POST['status_filter']) && trim($_POST['status_filter']) != '') {
                $statusFilterValue = $_POST['status_filter'];
                switch($statusFilterValue) {
                    case 'ready-dispatch':
                        $statusFilter .= " AND IFNULL(tfd.release_status,0) = 0";
                        break;
                    case 'dispatched':
                        $statusFilter .= " AND IFNULL(tfd.release_status,0) = 1";
                        break;
                }
            }
            break;
            
        case 'pickup_deliver':
            $leftJoin = " LEFT JOIN tf_dispatch tfd ON tfd.drno=tfs.drno";
            $leftJoin .= " LEFT JOIN tf_consolidate tfc ON tfc.csmno=tfs.csmno";
            $leftJoin .= " LEFT JOIN web_mf_partners sp ON sp.sp_id=tfc.sp_id";
            
            // Base filter for pickup & deliver shipments (consolidated or ready for consolidation)
            $statusFilter = " AND (wtfs.shipment_movement_status = ".SHIPMENT_CONSOLIDATED_ID;
            $statusFilter .= " OR (wtfs.shipment_movement_status = ".WAYBILL_PRINTED_ID." AND COALESCE(tfs.csmno,'') = ''))";
            
            // Add specific status filtering if requested
            if(isset($_POST['status_filter']) && trim($_POST['status_filter']) != '') {
                $statusFilterValue = $_POST['status_filter'];
                switch($statusFilterValue) {
                    case 'ready-consolidate':
                        $statusFilter = " AND wtfs.shipment_movement_status = ".WAYBILL_PRINTED_ID." AND COALESCE(tfs.csmno,'') = ''";
                        break;
                    case 'consolidated':
                        $statusFilter = " AND wtfs.shipment_movement_status = ".SHIPMENT_CONSOLIDATED_ID;
                        break;
                }
            }
            break;
            
        default:
            $statusFilter = " AND wtfs.shipment_movement_status >= 0"; // All statuses
    }
}

// counts_only handling
if(isset($_POST['counts_only'])) {
    $counts = array(
        'direct_delivery' => 0,
        'pickup_deliver' => 0
    );
    
    // Count Direct Delivery shipments
    $counts['direct_delivery'] = mysql_result(doQuery("SELECT COUNT(DISTINCT wtfs.shipment_magic) as c FROM tf_shipments tfs LEFT JOIN web_tf_shipments wtfs ON tfs.billed=wtfs.shipment_magic LEFT JOIN tf_dispatch tfd ON tfd.drno=tfs.drno WHERE (COALESCE(tfs.account,'')<>'') AND wtfs.shipment_movement_status=".SHIPMENT_DISPATCHED_STATUS_ID." AND tfd.drtype='DIRECT DELIVERY TO CONSIGNEE'".$queryFilters),0,'c');
    
    // Count Pickup & Deliver shipments
    $counts['pickup_deliver'] = mysql_result(doQuery("SELECT COUNT(DISTINCT wtfs.shipment_magic) as c FROM tf_shipments tfs LEFT JOIN web_tf_shipments wtfs ON tfs.billed=wtfs.shipment_magic LEFT JOIN tf_consolidate tfc ON tfc.csmno=tfs.csmno WHERE (COALESCE(tfs.account,'')<>'') AND (wtfs.shipment_movement_status=".SHIPMENT_CONSOLIDATED_ID." OR (wtfs.shipment_movement_status=".WAYBILL_PRINTED_ID." AND COALESCE(tfs.csmno,'')=''))".$queryFilters),0,'c');
    
    // Add detailed status breakdown based on tab
    if(isset($_POST['tab_click'])) {
        if($_POST['tab_click']=='direct_delivery') {
            $status_breakdown = array(
                'ready-dispatch' => array('count' => 0, 'name' => 'Ready for Dispatch'),
                'dispatched' => array('count' => 0, 'name' => 'Dispatched')
            );
            
            $baseQuery = "SELECT COUNT(DISTINCT wtfs.shipment_magic) as c FROM tf_shipments tfs LEFT JOIN web_tf_shipments wtfs ON tfs.billed=wtfs.shipment_magic LEFT JOIN tf_dispatch tfd ON tfd.drno=tfs.drno WHERE (COALESCE(tfs.account,'')<>'') AND wtfs.shipment_movement_status=".SHIPMENT_DISPATCHED_STATUS_ID." AND tfd.drtype='DIRECT DELIVERY TO CONSIGNEE'";
            
            $status_breakdown['ready-dispatch']['count'] = mysql_result(doQuery($baseQuery." AND IFNULL(tfd.release_status,0)=0".$queryFilters),0,'c');
            $status_breakdown['dispatched']['count'] = mysql_result(doQuery($baseQuery." AND IFNULL(tfd.release_status,0)=1".$queryFilters),0,'c');
            
            $counts['status_breakdown'] = $status_breakdown;
        } elseif($_POST['tab_click']=='pickup_deliver') {
            $status_breakdown = array(
                'ready-consolidate' => array('count' => 0, 'name' => 'Ready for Consolidation'),
                'consolidated' => array('count' => 0, 'name' => 'Consolidated')
            );
            
            $status_breakdown['ready-consolidate']['count'] = mysql_result(doQuery("SELECT COUNT(DISTINCT wtfs.shipment_magic) as c FROM tf_shipments tfs LEFT JOIN web_tf_shipments wtfs ON tfs.billed=wtfs.shipment_magic WHERE (COALESCE(tfs.account,'')<>'') AND wtfs.shipment_movement_status=".WAYBILL_PRINTED_ID." AND COALESCE(tfs.csmno,'')=''".$queryFilters),0,'c');
            $status_breakdown['consolidated']['count'] = mysql_result(doQuery("SELECT COUNT(DISTINCT wtfs.shipment_magic) as c FROM tf_shipments tfs LEFT JOIN web_tf_shipments wtfs ON tfs.billed=wtfs.shipment_magic WHERE (COALESCE(tfs.account,'')<>'') AND wtfs.shipment_movement_status=".SHIPMENT_CONSOLIDATED_ID.$queryFilters),0,'c');
            
            $counts['status_breakdown'] = $status_breakdown;
        }
    }
    
    echo json_encode($counts);
    exit;
}

// Main query for data
$querysqlfinal = "SELECT
                     tfs.account as client,
                     tfs.orderno as order_number,
                     tfs.waybillno as waybill_number,
                     tfs.csmno as csmno,
                     IF((ISNULL(tfs.pickupdate)) or (trim(tfs.pickupdate) = '0000-00-00'),tfs.rcvdate,tfs.pickupdate) as pickup_date,
                     tfs.poddate,
                     wtfs.shipment_magic as shipment_magic,
                     ROUND(IF(tfs.wgtvolume > 0, IF((tfs.wgtvolume/".VOLUME_METRIC_DIVISOR.") > tfs.wgt,(tfs.wgtvolume/".VOLUME_METRIC_DIVISOR."),tfs.wgt),
                      IF(((tfs.len*tfs.wid*tfs.hgt)/".VOLUME_METRIC_DIVISOR.") > tfs.wgt,((tfs.len*tfs.wid*tfs.hgt)/".VOLUME_METRIC_DIVISOR."),tfs.wgt)),2) as chargable_weight,
                     TRIM(CONCAT(tfs.street,', ',tfs.brgymun,', ',tfs.cityprov,', ',tfs.zipcode)) as destination,
                     tfs.consignee,
                     DATEDIFF(CURDATE(), DATE(IF((ISNULL(tfs.pickupdate)) or (trim(tfs.pickupdate) = '0000-00-00'),tfs.rcvdate,tfs.pickupdate))) as aging_days,
                     wtfs.shipment_movement_status as status_index,
                     ".$selectQuery."
                     FROM tf_shipments tfs 
                     LEFT JOIN web_tf_shipments wtfs ON tfs.billed=wtfs.shipment_magic
                     ".$leftJoin."
                     WHERE wtfs.shipment_magic>0 
                     AND wtfs.if_parent=0
                     AND (COALESCE(tfs.account,'')<>'')
                     ".$statusFilter.$queryFilters.QueryFilterByDepartment('departmentID','wtfs','AND');

// Search functionality
$querywheresearch = '';
if(isset($_REQUEST['search']['value']) && trim($_REQUEST['search']['value']) != '') {
    $searchstring = FieldDBInput($_REQUEST['search']['value']);
    $querywheresearch = " AND (";
    $querywheresearch .= " (tfs.account LIKE '".$searchstring."%')";
    $querywheresearch .= " OR (tfs.orderno LIKE '".$searchstring."%')";
    $querywheresearch .= " OR (tfs.waybillno LIKE '".$searchstring."%')";
    $querywheresearch .= " OR (tfs.consignee LIKE '".$searchstring."%')";
    $querywheresearch .= " OR (TRIM(CONCAT(tfs.street,', ',tfs.brgymun,', ',tfs.cityprov,', ',tfs.zipcode)) LIKE '".$searchstring."%')";
    $querywheresearch .= ")";
}

// Count query for total records
$querysqlAll = "SELECT COUNT(DISTINCT wtfs.shipment_magic) as counter FROM tf_shipments tfs LEFT JOIN web_tf_shipments wtfs ON tfs.billed=wtfs.shipment_magic ".$leftJoin." WHERE wtfs.shipment_magic>0 AND wtfs.if_parent=0 AND (COALESCE(tfs.account,'')<>'') ".$statusFilter.$queryFilters.QueryFilterByDepartment('departmentID','wtfs','AND');

// Get total count
$queryAll = doQuery($querysqlAll . $querywheresearch);
$totalData = 0;
if(mysql_num_rows($queryAll) > 0) {
    $totalData = mysql_result($queryAll, 0, 'counter');
}

// Add search to main query
$querysqlfinal .= $querywheresearch;

// Ordering
$orderby = '';
if(isset($_REQUEST['order']) && is_array($_REQUEST['order'])) {
    for($xx = 0; $xx < sizeof($_REQUEST['order']); $xx++) {
        if($orderby<>'') {
            $orderby .= ',';
        }
        $columnIndex = (int)$_REQUEST['order'][$xx]['column'];
        $direction = $_REQUEST['order'][$xx]['dir'];
        
        // Map column index to actual column names (10 columns for both tabs)
        switch($columnIndex) {
            case 0: // Client
                $orderby .= "tfs.account ".$direction;
                break;
            case 1: // Order #
                $orderby .= "tfs.orderno ".$direction;
                break;
            case 2: // Waybill #
                $orderby .= "tfs.waybillno ".$direction;
                break;
            case 3: // Chargeable Weight
                $orderby .= "chargable_weight ".$direction;
                break;
            case 4: // Destination
                $orderby .= "TRIM(CONCAT(tfs.street,tfs.brgymun,tfs.cityprov,tfs.zipcode)) ".$direction;
                break;
            case 5: // Consignee
                $orderby .= "tfs.consignee ".$direction;
                break;
            case 6: // Date
                $orderby .= "pickup_date ".$direction;
                break;
            case 7: // Days Aging
                $orderby .= "aging_days ".$direction;
                break;
            case 8: // Courier/Service Partner
                if($_POST['status'] == 'direct_delivery') {
                    $orderby .= "tfd.updateacceptedby ".$direction;
                } else {
                    $orderby .= "tfc.spname ".$direction;
                }
                break;
            case 9: // Status
                $orderby .= "wtfs.shipment_movement_status ".$direction;
                break;
            default:
                $orderby .= "aging_days ".$direction;
        }
    }
}
if($orderby<>'') {
    $orderby = " ORDER BY ".$orderby;
} else {
    $orderby = " ORDER BY aging_days ASC";
}

// Pagination
$limitoffset = " LIMIT ".(int)$_REQUEST['start'].", ".(int)$_REQUEST['length'];
if((int)$_REQUEST['length'] == -1) {
    $limitoffset = '';
}

$querysqlfinal .= $orderby . $limitoffset;

// Execute query
$query = doQuery($querysqlfinal);

$data = array();
while($row = mysql_fetch_array($query)) {
    $nestedData = array();
    
    $nestedData[] = $row['client'];
    $nestedData[] = '<a title="View Transaction Details" href="javascript:ShowTransDetails(\'details.php?shipment_magic='.$row['shipment_magic'].'\',\'Details of Order#'.$row['order_number'].'\')">'.$row['order_number'].'</a>';
    $nestedData[] = $row['waybill_number'];
    $nestedData[] = number_format($row['chargable_weight'], 2) . ' kg';
    $nestedData[] = $row['destination'];
    $nestedData[] = $row['consignee'];
    $nestedData[] = FormatDate($row['pickup_date'], 5);
    $nestedData[] = (int)$row['aging_days'] . ' days';
    
    // Courier/Service Partner column
    if($_POST['status'] == 'direct_delivery') {
        $nestedData[] = $row['updateacceptedby'] ?: '<span class="text-muted">Not assigned</span>';
    } else {
        $nestedData[] = $row['spname'] ?: '<span class="text-muted">Not assigned</span>';
    }
    
    // Status column
    $status_desc = GiveDeliveryStatus($row['status_index']);
    if($_POST['status'] == 'direct_delivery') {
        if($row['release_status'] == 1) {
            $nestedData[] = '<span class="label label-success">DISPATCHED</span>';
        } else {
            $nestedData[] = '<span class="label label-warning">READY FOR DISPATCH</span>';
        }
    } else {
        if($row['status_index'] == SHIPMENT_CONSOLIDATED_ID) {
            $nestedData[] = '<span class="label label-success">CONSOLIDATED</span>';
        } else {
            $nestedData[] = '<span class="label label-warning">READY FOR CONSOLIDATION</span>';
        }
    }
    
    $data[] = $nestedData;
}

// Return JSON response for DataTables
$json_data = array(
    "draw"            => intval($_REQUEST['draw']),
    "recordsTotal"    => intval($totalData),
    "recordsFiltered" => intval($totalData),
    "data"            => $data,
    "counts"          => []
);

echo json_encode($json_data);
?>
