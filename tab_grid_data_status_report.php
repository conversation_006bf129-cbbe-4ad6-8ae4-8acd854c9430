<?php
$_GET['donotshowheader'] = 1;
	include('header.php');
	include('session.php');

$sortfield = 'tfs.pickupdate DESC, tfs.orderno';
						    
$queryFilters = '';
	if(LWLI_CLIENT_ONLINE<>ADMIN_USERS_COMPANY && trim(LWLI_CLIENT_ONLINE)<>'') {
$queryFilters .= " AND UPPER(TRIM(tfs.account))=UPPER(TRIM('".FieldDBInput(LWLI_CLIENT_ONLINE)."'))";
	}

$filterdate='';
if((isset($_GET['from']) && $_GET['from']<>'') && (isset($_GET['to']) && $_GET['to']<>'') ){
	$filterdate='AND tfs.pickupdate BETWEEN "'.$_GET['from'].'" AND "'.$_GET['to'].'" ';
} 

$querysqlAll = "SELECT wtfs.shipment_movement_status as status_index 
		FROM tf_shipments tfs 
		LEFT JOIN web_tf_shipments wtfs on tfs.billed=wtfs.shipment_magic 
		LEFT JOIN tf_consolidate tfc on tfc.csmno=tfs.csmno
		LEFT JOIN tf_dispatch tfd on tfd.drno=tfs.drno 
		WHERE wtfs.shipment_magic>0 ".$queryFilters.$filterdate." 
		AND (wtfs.shipment_movement_status BETWEEN ".PRE_ALERTED_STATUS_ID." AND ".DELIVERED_STATUS_ID.")  ".
		QueryFilterByDepartment('departmentID','wtfs','AND'). "
		GROUP BY tfs.waybillno,tfs.orderno ";

$queryAll = doQuery($querysqlAll);
$queryAllRows = mysql_num_rows($queryAll);
$totalData = $queryAllRows;
				
$querysqlfinal = "SELECT IF((ISNULL(tfs.pickupdate)) or (trim(tfs.pickupdate) = '0000-00-00'),tfs.rcvdate,tfs.pickupdate) as pickup_date,
										tfs.account as account,
										wtfs.shipment_magic as shipment_magic,
										tfs.rcvdate as scanned_in,
										IF(tfd.drtype='DIRECT DELIVERY',tfs.drdate,tfs.csmdate) as scanned_out,
										tfs.shipdate as shipping_date,
										tfs.ragdate as received_gateway,
										tfs.receivedby as received_by,
										tfs.poddate as delivery_date,
										tfs.waybillno as waybill_no,
										tfs.orderno as order_number,
										tfs.wgt as actual_weight,
										tfs.consignee as consignee,
										tfs.cityprov as city_province,
										tfs.street as address,
										IF(tfs.billed=1,'YES','NO') as bill_voice,
										tfd.destination as provider,
										tfd.drno as dr_number,
										tfc.spname as service_partner,
										ROUND(IF(tfs.wgtvolume > 0,(tfs.wgtvolume/".VOLUME_METRIC_CBM_DIVISOR."),((tfs.len/".CBM_LENGTH_WIDTH_HEIGHT_DIVISOR.")*(tfs.wid/".CBM_LENGTH_WIDTH_HEIGHT_DIVISOR.")*(tfs.hgt/".CBM_LENGTH_WIDTH_HEIGHT_DIVISOR."))),3) as cbm,
										CONCAT(tfs.len,' x ', tfs.wid,' x ',tfs.hgt) as measurement,
										ROUND(IF(tfs.wgtvolume > 0,(tfs.wgtvolume/".VOLUME_METRIC_DIVISOR."),((tfs.len*tfs.wid*tfs.hgt)/".VOLUME_METRIC_DIVISOR.")),2) as volumetric_charge,
										ROUND(IF(tfs.wgtvolume > 0, IF((tfs.wgtvolume/".VOLUME_METRIC_DIVISOR.") > tfs.wgt,(tfs.wgtvolume/".VOLUME_METRIC_DIVISOR."),tfs.wgt),
					  					  IF(((tfs.len*tfs.wid*tfs.hgt)/".VOLUME_METRIC_DIVISOR.") > tfs.wgt,((tfs.len*tfs.wid*tfs.hgt)/".VOLUME_METRIC_DIVISOR."),tfs.wgt)),2) as chargable_weight,
										'' as remarks,
										tfs.content as items,
										tfs.rcvqty as quantity,
										wtfs.shipment_movement_status as status_index,
										tfs.status as status_word,
										CONCAT(tfd.courier,' ',tfd.plateno) as plateno,
										DATEDIFF(IF(tfd.drtype='DIRECT DELIVERY',tfs.drdate,tfs.csmdate),tfs.rcvdate) as aging_days,
										tfd.updateacceptedby,
										tfc.spname,
										IF(tfd.drtype='DIRECT DELIVERY',tfd.updateacceptedby,tfc.spname) as courier_name
										FROM tf_shipments tfs 
										LEFT JOIN web_tf_shipments wtfs on tfs.billed=wtfs.shipment_magic
										LEFT JOIN tf_consolidate tfc on tfc.csmno=tfs.csmno
										LEFT JOIN tf_dispatch tfd on tfd.drno=tfs.drno 
										WHERE wtfs.shipment_magic>0 
										AND wtfs.if_parent=0
										AND (wtfs.shipment_movement_status BETWEEN ".PRE_ALERTED_STATUS_ID." AND ".DELIVERED_STATUS_ID.")  ".
		                                                                QueryFilterByDepartment('departmentID','wtfs','AND'). 
		                                                                $queryFilters.$filterdate;

//if($_SESSION['ShowColumnDateUpdate']==1) {
		$columns = array( 
		  0 => 'tfs.account',
		  1 => 'pickup_date',
		  2 => 'tfs.rcvdate',
		  3 => 'tfs.drdate',
		  4 => 'tfs.shipdate',
		  5 => 'tfd.destination',
		  6 => 'tfs.ragdate',
		  7 => 'tfs.receivedby',
		  8 => 'tfs.poddate',
		  9 => 'bill_voice',
		  10 => 'tfs.waybillno',
		  11 => 'tfs.drno',
		  12 => 'tfs.orderno',
		  13 => 'tfs.content',
		  14 => 'tfs.rcvqty',
		  15 => 'tfs.wgt',
		  16 => 'cbm',
		  17 => 'measurement',
		  18 => 'volumetric_charge',
		  19 => 'tfs.wgt',
		  20 => 'tfs.consignee',
		  21 => 'tfs.cityprov',
		  22 => 'tfs.street',
		  23 => 'tfs.courier_name',
		  24 => 'tfs.status',
		  25 => 'status_index'
);
		
		
	$filtercondition = 'AND';
	if($queryFilters<>'')
$filtercondition = ' AND ';
$searchstring = FieldDBInput($_REQUEST['search']['value']);
$querysearchfilter = "";
			
	if(trim($searchstring)<>'') {     // if there is a search parameter, $requestData['search']['value'] contains search parameter
$querysearchfilter .= $filtercondition;  
$querysearchfilter.=" ( (tfs.account LIKE '".$searchstring."%') ";    
$querysearchfilter.=" OR (DATE_FORMAT(pickup_date,'%d-%b-%y') LIKE '".$searchstring."%')";
$querysearchfilter.=" OR (DATE_FORMAT(tfs.rcvdate,'%d-%b-%y') LIKE '".$searchstring."%') ";
$querysearchfilter.=" OR (DATE_FORMAT(tfs.drdate,'%d-%b-%y') LIKE '".$searchstring."%') ";
$querysearchfilter.=" OR (DATE_FORMAT(tfs.shipdate,'%d-%b-%y') LIKE '".$searchstring."%') ";
$querysearchfilter.=" OR (tfd.destination LIKE '".$searchstring."%') ";
$querysearchfilter.=" OR (DATE_FORMAT(tfs.ragdate,'%d-%b-%y') LIKE '".$searchstring."%') ";
$querysearchfilter.=" OR (tfs.receivedby LIKE '".$searchstring."%') ";
$querysearchfilter.=" OR (DATE_FORMAT(tfs.poddate,'%d-%b-%y') LIKE '".$searchstring."%') ";
$querysearchfilter.=" OR (tfs.content LIKE '".$searchstring."%') ";
$querysearchfilter.=" OR (tfs.rcvqty LIKE '".$searchstring."%') ";
$querysearchfilter.=" OR (tfs.wgt LIKE '".$searchstring."%') ";
$querysearchfilter.=" OR (tfs.orderno LIKE '".$searchstring."%') ";
$querysearchfilter.=" OR (tfs.waybillno LIKE '".$searchstring."%') ";
$querysearchfilter.=" OR (tfs.drno LIKE '".$searchstring."%') ";
$querysearchfilter.=" OR (tfs.consignee LIKE '".$searchstring."%') ";
$querysearchfilter.=" OR (tfs.cityprov LIKE '".$searchstring."%') ";
$querysearchfilter.=" OR (tfs.street LIKE '".$searchstring."%') ";
$querysearchfilter.=" OR (DATE_FORMAT(tfs.poddate,'%d-%b-%y') LIKE '".$searchstring."%') ";
$querysearchfilter.=" OR (tfs.receivedby LIKE '".$searchstring."%') ";
$querysearchfilter.=" OR (status_word LIKE '".$searchstring."%') ";
$querysearchfilter.= ") ";
	}
		
/*} else {
	  $columns = array( 
						0 => 'tfs.orderno', 
	     1 => 'tfs.consignee',
	     2 => 'tfs.cityprov',
	     3 => 'tfs.street',
					 4 => 'status_index'
		 );
	 
	     
if(trim($searchstring)<>'') {  // if there is a search parameter, $requestData['search']['value'] contains search parameter
$querysearchfilter .= $filtercondition;
$querysearchfilter.=" ( (tfs.orderno LIKE '".$searchstring."%') ";
$querysearchfilter.=" OR (tfs.consignee LIKE '".$searchstring."%') ";
$querysearchfilter.=" OR (tfs.cityprov LIKE '".$searchstring."%')";
$querysearchfilter.=" OR (tfs.street LIKE '".$searchstring."%') ";
$querysearchfilter.=" OR (status_word LIKE '".$searchstring."%') ";
$querysearchfilter.= ") ";
		 }
	}*/

$querysqlfinal .= ' GROUP BY wtfs.shipment_magic '.$querysearchfilter;

$query=doQuery($querysqlfinal);
$totalFiltered = mysql_num_rows($query); // when there is a search parameter then we have to modify total number filtered rows as per search result. 

$orderby = '';
for($xx = 0; $xx < sizeof($_REQUEST['order']); $xx++) {
  if($orderby<>'')
    $orderby .= ',';
  $orderby .= $columns[(int)$_REQUEST['order'][$xx]['column']]." ".$_REQUEST['order'][$xx]['dir'];
}
if($orderby<>'')
  $orderby = " ORDER BY ".$orderby;

$limitoffset = " LIMIT ".(int)$_REQUEST['start'].", ".(int)$_REQUEST['length'];

if((int)$_REQUEST['length'] == -1)
  $limitoffset = '';
 
//echo $limitoffset;exit;
$querysqlfinal.= $orderby . $limitoffset;

//echo $querysqlfinal;exit; 
$query=doQuery($querysqlfinal);


$data = array();
while($row=mysql_fetch_array($query)) {  // preparing an array
$nestedData=array(); 

$nestedData[] = $row['account'];
$nestedData[] = FormatDate($row['pickup_date'],5);
$nestedData[] = FormatDate($row['scanned_in'],5);

if(trim(FormatDate($row['scanned_out'],5)) == '') {
  //if(LWLI_CLIENT_ONLINE!='EOP')										  
    //$nestedData[] = date("d-M-y"); 
  //else
    $nestedData[] = "&nbsp;";											 
} else {
   $nestedData[] = FormatDate($row['scanned_out'],5); 
}

$nestedData[] = $row['aging_days'];

if(trim(FormatDate($row['shipping_date'])) == '') {
  if(LWLI_CLIENT_ONLINE!='EOP')										  
    $nestedData[] = date("d-M-y");
  else
    $nestedData[] = "&nbsp;";											 
} else {
  $nestedData[] = FormatDate($row['shipping_date'],5); 
}

$nestedData[] = $row['provider'];
	 
if(trim(FormatDate($row['received_gateway'],5)) == '') {
  if(LWLI_CLIENT_ONLINE!='EOP')										  
    $nestedData[] = date("d-M-y");
  else
    $nestedData[] = "&nbsp;";											 
} else {
  $nestedData[] = FormatDate($row['received_gateway'],5); 
}

$nestedData[] = $row['received_by'];
$nestedData[] = FormatDate($row['delivery_date'],5);
$nestedData[] = $row['bill_voice'];
$nestedData[] = $row['waybill_no'];
$nestedData[] = $row['dr_number'];
$nestedData[] = $row['order_number'];
$nestedData[] = $row['items']; 
$nestedData[] = $row['quantity']; 
$nestedData[] = $row['actual_weight'];
$nestedData[] = $row['cbm'];
$nestedData[] = $row['measurement'];
$nestedData[] = $row['volumetric_charge'];
$nestedData[] = $row['chargable_weight'];
$nestedData[] = $row['consignee'];
$nestedData[] = $row['city_province'];
$nestedData[] = $row['address'];
$nestedData[] = $row['courier_name'];
$nestedData[] = $row['remarks'];

$datashow = '<td><a title="View Transaction Details" href="javascript:ShowTransDetails(\'details.php?shipment_magic='.$row['shipment_magic'].'\',\'Details of Order#'.$row['order_number'].'\')">
             <i class="icon-info-sign icon-large"></i></a></td>';

$status_desc = GiveDeliveryStatus($row['status_index']);
if($row['status_index']==DELIVERED_STATUS_ID)
$nestedData[] = '<span class="statusdetails  status8">'.strtoupper($status_desc).'</span>'.$datashow;
elseif($row['status_index']==RECEIVED_AT_GATEWAY_STATUS_ID)
$nestedData[] = '<span class="statusdetails  status7">'.strtoupper($status_desc).'</span>'.$datashow;
elseif($row['status_index']==TRANSFER_TO_PROVIDER_ID)
$nestedData[] = '<span class="statusdetails  status6">'.strtoupper($status_desc).'</span>'.$datashow;
elseif($row['status_index']==SHIPMENT_DISPATCHED_STATUS_ID)
$nestedData[] = '<span class="statusdetails  status5">'.strtoupper($status_desc).'</span>'.$datashow;
elseif($row['status_index']==SHIPMENT_CONSOLIDATED_ID)
$nestedData[] = '<span class="statusdetails  status4">'.strtoupper($status_desc).'</span>'.$datashow;
elseif($row['status_index']==WAYBILL_PRINTED_ID)
$nestedData[] = '<span class="statusdetails  status3">'.strtoupper($status_desc).'</span>'.$datashow;
elseif($row['status_index']==SCAN_IN_TO_HUB_STATUS_ID)
$nestedData[] = '<span class="statusdetails  status2">'.strtoupper($status_desc).'</span>'.$datashow;		
elseif($row['status_index']==SHIPMENT_PICKED_UP_STATUS_ID)
$nestedData[] = '<span class="statusdetails  status1">'.strtoupper($status_desc).'</span>'.$datashow;
elseif($row['status_index']==PRE_ALERTED_STATUS_ID)
$nestedData[] = '<span class="statusdetails  status0">'.strtoupper($status_desc).'</span>'.$datashow;
else
$nestedData[] = '<span class="statusdetails  status0">'.strtoupper($status_desc).'</span>'.$datashow;
  
	
$data[] = $nestedData;
}

$json_data = array(
			"draw"            => intval( $_REQUEST['draw'] ),   // for every request/draw by clientside , they send a number as a parameter, when they recieve a response/data they first check the draw number, so we are sending same number in draw. 
			"recordsTotal"    => intval( $totalData ),  // total number of records
			"recordsFiltered" => intval( $totalFiltered ), // total number of records after searching, if there is no searching then totalFiltered = totalData
			"data"            => $data   // total data array
			);

echo json_encode($json_data);  
?>

