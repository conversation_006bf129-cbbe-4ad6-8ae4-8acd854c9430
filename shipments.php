<?php
if(isset($_GET['dynamicDataProvider'])) {
  $_GET['donotshowheader'] = 1;
}

include('header.php');
include('session.php');

?>

<body>
    <style>
        /* Status badge styling */
        .badge {
            transition: all 0.2s ease;
            cursor: pointer;
            white-space: nowrap;
        }
        .badge:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        .badge-info {
            background-color: #5bc0de !important;
        }
        .badge-primary {
            background-color: #337ab7 !important;
        }
        /* Custom styling for To Ship tab badges */
        .status-badge-black {
            background-color: #000 !important;
            color: #ffff00 !important;
        }
        .status-count-red {
            background-color: #dc3545 !important;
            color: white !important;
            padding: 2px 6px;
            border-radius: 8px;
            margin-left: 4px;
            font-weight: bold;
        }
        /* Status filter badge styling */
        .status-filter-badge {
            transition: all 0.3s ease;
        }
        .status-filter-badge:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .status-filter-badge.active {
            background-color: #007bff !important;
            color: white !important;
            box-shadow: 0 4px 8px rgba(0,123,255,0.3);
        }

        /* Custom loading overlay */
        .custom-loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .custom-loading-content {
            background-color: #fff;
            padding: 30px 40px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .custom-loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Hide default DataTables processing indicator */
        .dataTables_processing {
            display: none !important;
        }

        /* Enhanced status label colors */
        .label {
            font-size: 11px;
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
            text-transform: uppercase;
        }

        .label-info {
            background-color: #5bc0de !important; /* Light Blue */
            color: white !important;
        }

        .label-success {
            background-color: #5cb85c !important; /* Green */
            color: white !important;
        }

        .label-warning {
            background-color: #f0ad4e !important; /* Orange */
            color: white !important;
        }

        .label-danger {
            background-color: #d9534f !important; /* Red */
            color: white !important;
        }

        .label-primary {
            background-color: #337ab7 !important; /* Blue */
            color: white !important;
        }

        .label-default {
            background-color: #777 !important; /* Gray */
            color: white !important;
        }
    </style>
    <?php include('navbar_menu.php'); ?>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <br><br><br>
                
                <h3 class="page-header">My Shipments</h3>
                
                <!-- Tab Navigation -->
                <ul class="nav nav-tabs" id="shipmentTabs" role="tablist">
                  
                    <?php if(!IsSP() && !IsCourierContractor() && !IsSPCourier()) { ?>
                      
                    <li role="presentation" class="active">
                        <a href="#prealerted" id="prealerted-tab" role="tab" data-toggle="tab" aria-controls="prealerted" aria-expanded="true">
                            <i class="fa fa-bell"></i> Pre-alerted
                            <span class="badge" id="badge-prealerted"><i class="icon-spinner icon-spin"></i></span>
                        </a>
                    </li>
                    
                    <li role="presentation">
                       <a href="#toShip" id="toShip-tab" role="tab" data-toggle="tab" aria-controls="toShip" aria-expanded="false">
                            <i class="fa fa-ship"></i> To Ship
                            <span class="badge" id="badge-toShip"><i class="icon-spinner icon-spin"></i></span>
                       </a>
                    </li>
                    
                    <?php } ?>
                    
                    <li role="presentation">
                       <a href="#transferToProvider" id="transferToProvider-tab" role="tab" data-toggle="tab" aria-controls="transferToProvider" aria-expanded="false">
                            <i class="fa fa-exchange"></i> Transfer to Provider
                            <span class="badge" id="badge-transferToProvider"><i class="icon-spinner icon-spin"></i></span>
                       </a>
                    </li>
                    <li role="presentation">
                       <a href="#toReceive" id="toReceive-tab" role="tab" data-toggle="tab" aria-controls="toReceive" aria-expanded="false">
                            <i class="fa fa-download"></i> To Receive
                            <span class="badge" id="badge-toReceive"><i class="icon-spinner icon-spin"></i></span>
                       </a>
                    </li>
                   
                    <li role="presentation">
                        <a href="#delivered" id="delivered-tab" role="tab" data-toggle="tab" aria-controls="delivered" aria-expanded="false">
                            <i class="fa fa-check-circle"></i> Delivered
                            <span class="badge" id="badge-delivered"><i class="icon-spinner icon-spin"></i></span>
                        </a>
                    </li>
                    <li role="presentation">
                        <a href="#allDelivered" id="allDelivered-tab" role="tab" data-toggle="tab" aria-controls="allDelivered" aria-expanded="false">
                            <i class="fa fa-list"></i> All Delivered
                        </a>
                    </li>
                </ul>
                </br>
                <!-- Tab Content -->
                <div class="tab-content" id="shipmentTabContent">
                    <!-- Pre-alerted Tab -->
                    <div role="tabpanel" class="tab-pane fade in active" id="prealerted" aria-labelledby="prealerted-tab">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h3 class="panel-title">
                                    <i class="fa fa-bell text-warning"></i> Pre-alerted Shipments
                                    <span style="color: #666; font-size: 14px;">(Shipments that have been pre-alerted and are awaiting processing)</span>
                                </h3>
                            </div>
                            <div class="panel-body">
                                <!-- Status Badges Row -->
                                <div class="row" style="margin-bottom: 20px;">
                                    <div class="col-md-12">
                                        <div style="display: flex; flex-wrap: wrap; gap: 8px; align-items: center; justify-content: center;">
                                            <span class="badge status-filter-badge" data-status="expired" style="font-size: 12px; padding: 6px 10px; border-radius: 12px; cursor: pointer; margin: 0 5px;" title="Click to filter by this status">
                                                <span id="badge-expired-text">Expired</span> <span id="count-expired" class="btn-warning" style="color: black; padding: 2px 6px; border-radius: 8px; margin-left: 4px;"><i class="icon-spinner icon-spin"></i></span>
                                            </span>
                                            <span class="badge status-filter-badge" data-status="current" style="font-size: 12px; padding: 6px 10px; border-radius: 12px; cursor: pointer; margin: 0 5px;" title="Click to filter by this status">
                                                <span id="badge-current-text">Current</span> <span id="count-current" class="btn-warning" style="color: black; padding: 2px 6px; border-radius: 8px; margin-left: 4px;"><i class="icon-spinner icon-spin"></i></span>
                                            </span>
                                            <span class="badge status-filter-badge" data-status="future" style="font-size: 12px; padding: 6px 10px; border-radius: 12px; cursor: pointer; margin: 0 5px;" title="Click to filter by this status">
                                                <span id="badge-future-text">Future</span> <span id="count-future" class="btn-warning" style="color: black; padding: 2px 6px; border-radius: 8px; margin-left: 4px;"><i class="icon-spinner icon-spin"></i></span>
                                            </span>
                                            <span class="badge status-filter-badge" data-status="no-schedule" style="font-size: 12px; padding: 6px 10px; border-radius: 12px; cursor: pointer; margin: 0 5px;" title="Click to filter by this status">
                                                <span id="badge-no-schedule-text">No assign schedule of pickup date</span> <span id="count-no-schedule" class="btn-warning" style="color: black; padding: 2px 6px; border-radius: 8px; margin-left: 4px;"><i class="icon-spinner icon-spin"></i></span>
                                            </span>
                                            <span class="badge" id="status-filter-badge_prealerted" data-status="show-all" style="font-size: 12px; padding: 6px 10px; border-radius: 12px; cursor: pointer; background-color: #6c757d; color: white; margin-left: 10px;" title="Click to show all statuses">
                                                Show All
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover" id="prealertedTable">
                                                <thead>
                                                    <tr>
                                                        <th>Client</th>
                                                        <th>Order #</th>
                                                        <th>Chargeable Weight</th>
                                                        <th>Destination</th>
                                                        <th>Consignee</th>
                                                        <th>Pre-alert Date</th>
                                                        <th>Schedule of Pickup Date</th>
                                                        <th>Days Aging</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- To Ship Tab -->
                    <div role="tabpanel" class="tab-pane fade" id="toShip" aria-labelledby="toShip-tab">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h3 class="panel-title">
                                    <i class="fa fa-ship text-primary"></i> Shipments Ready to Ship
                                    <span style="color: #666; font-size: 14px;">(Shipments that are ready to be shipped out)</span>
                                </h3>
                            </div>
                            <div class="panel-body">
                                <!-- Status Badges Row -->
                                <div class="row" style="margin-bottom: 20px;">
                                    <div class="col-md-12">
                                        <div style="display: inline-block; gap: 8px; text-align: center; width: 100%;">
                                            <span class="badge status-filter-badge" data-status="picked_up" style="font-size: 12px; padding: 6px 10px; border-radius: 12px; cursor: pointer; margin: 0 5px;" title="Click to filter by this status">
                                                <span id="badge-picked-up-text">Picked Up</span> <span id="count-picked-up" style="background-color: #98FB98; color: black; padding: 2px 6px; border-radius: 8px; margin-left: 4px;"><i class="icon-spinner icon-spin"></i></span>
                                            </span>
                                            <span class="badge status-filter-badge" data-status="scan_hub" style="font-size: 12px; padding: 6px 10px; border-radius: 12px; cursor: pointer; margin: 0 5px;" title="Click to filter by this status">
                                                <span id="badge-scan-hub-text">Scan in Hub</span> <span id="count-scan-hub" style="background-color: #90EE90; color: black; padding: 2px 6px; border-radius: 8px; margin-left: 4px;"><i class="icon-spinner icon-spin"></i></span>
                                            </span>
                                            <span class="badge status-filter-badge" data-status="waybill_printed" style="font-size: 12px; padding: 6px 10px; border-radius: 12px; cursor: pointer; margin: 0 5px;" title="Click to filter by this status">
                                                <span id="badge-waybill-printed-text">Waybill Printed</span> <span id="count-waybill-printed" style="background-color: #0FFF50; color: black; padding: 2px 6px; border-radius: 8px; margin-left: 4px;"><i class="icon-spinner icon-spin"></i></span>
                                            </span>
                                            <span class="badge status-filter-badge" data-status="consolidated" style="font-size: 12px; padding: 6px 10px; border-radius: 12px; cursor: pointer; margin: 0 5px;" title="Click to filter by this status">
                                                <span id="badge-consolidated-text">Consolidated</span> <span id="count-consolidated" style="background-color: #0BDA51; color: black; padding: 2px 6px; border-radius: 8px; margin-left: 4px;"><i class="icon-spinner icon-spin"></i></span>
                                            </span>
                                            <span class="badge status-filter-badge" data-status="dispatched" style="font-size: 12px; padding: 6px 10px; border-radius: 12px; cursor: pointer; margin: 0 5px;" title="Click to filter by this status">
                                                <span id="badge-dispatched-text">For Dispatch</span> <span id="count-dispatched" style="background-color: #4CBB17; color: black; padding: 2px 6px; border-radius: 8px; margin-left: 4px;"><i class="icon-spinner icon-spin"></i></span>
                                            </span>
                                            <span class="badge" id="clear-status-filter" style="font-size: 12px; padding: 6px 10px; border-radius: 12px; cursor: pointer; background-color: #6c757d; color: white; margin-left: 10px;" title="Click to show all statuses">
                                                Show All
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover" id="toShipTable">
                                                <thead>
                                                    <tr>
                                                        <th>Client</th>
                                                        <th>Order #</th>
                                                        <th>Waybill #</th>
                                                        <th>Chargeable Weight</th>
                                                        <th>Destination</th>
                                                        <th>Consignee</th>
                                                        <th>Date</th>
                                                        <th>Days Aging</th>
                                                        <th>Mode of Delivery</th>
                                                        <th>Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <!-- Data will be loaded via AJAX -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Transfer to Provider Tab -->
                    <div role="tabpanel" class="tab-pane fade" id="transferToProvider" aria-labelledby="transferToProvider-tab">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h3 class="panel-title">
                                    <i class="fa fa-exchange text-warning"></i> Transfer to Provider
                                    <span style="color: #666; font-size: 14px;">(Shipments that are being transferred to the provider/gateway)</span>
                                </h3>
                            </div>
                            <div class="panel-body">
                                
                                 <?php if(!IsSP() && !IsCourierContractor() && IsSPCourier()) { ?>
                                 
                                <!-- Status Badges Row -->
                                <div class="row" style="margin-bottom: 20px;">
                                    <div class="col-md-12">
                                        <div style="display: flex; flex-wrap: wrap; gap: 8px; align-items: center; justify-content: center;">
                                            <span class="badge status-filter-badge" data-status="dispatched" style="font-size: 12px; padding: 6px 10px; border-radius: 12px; cursor: pointer; margin: 0 5px;" title="Click to filter by this status">
                                                <span id="badge-dispatched-text-tp">Dispatched</span> <span id="count-dispatched-tp" style="background-color: #90EE90; color: black; padding: 2px 6px; border-radius: 8px; margin-left: 4px;"><i class="icon-spinner icon-spin"></i></span>
                                            </span>
                                            <span class="badge status-filter-badge" data-status="on-the-way-to-gateway" style="font-size: 12px; padding: 6px 10px; border-radius: 12px; cursor: pointer; margin: 0 5px;" title="Click to filter by this status">
                                                <span id="badge-on-the-way-to-gateway-text">On the way to gateway</span> <span id="count-on-the-way-to-gateway" style="background-color: #0FFF50; color: black; padding: 2px 6px; border-radius: 8px; margin-left: 4px;"><i class="icon-spinner icon-spin"></i></span>
                                            </span>
                                            <span class="badge" id="status-filter-badge_transfer_to_provider" style="font-size: 12px; padding: 6px 10px; border-radius: 12px; cursor: pointer; background-color: #6c757d; color: white; margin-left: 10px;" title="Click to show all statuses">
                                                Show All
                                            </span>
                                        </div>
                                    </div>
                                </div>
                                
                                <?php } ?>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover" id="transferToProviderTable">
                                                <thead>
                                                    <tr>
                                                        <th>Client</th>
                                                        <th>Order #</th>
                                                        <th>Waybill #</th>
                                                        <th>Chargeable Weight</th>
                                                        <th>Destination</th>
                                                        <th>Consignee</th>
                                                        <th>Date</th>
                                                        <th>Days Aging</th>
                                                        <th>Mode of Delivery</th>
                                                        <th>Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <!-- Data will be loaded via AJAX -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- To Receive Tab -->
                    <div role="tabpanel" class="tab-pane fade" id="toReceive" aria-labelledby="toReceive-tab">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h3 class="panel-title">
                                    <i class="fa fa-download text-success"></i> Shipments to Receive
                                    <span style="color: #666; font-size: 14px;">(Shipments that are in transit and will be received soon)</span>
                                </h3>
                            </div>
                            <div class="panel-body">

                                <!-- Status Badges Row -->
                             
                                <?php if(!IsSP() && !IsCourierContractor() && IsSPCourier()) { ?>
                             
                                <div class="row" style="margin-bottom: 20px;">
                                    <div class="col-md-12">
                                        <div style="display: flex; flex-wrap: wrap; gap: 8px; align-items: center; justify-content: center;">
                                            <span class="badge status-filter-badge" data-status="out-direct-delivery" style="font-size: 12px; padding: 6px 10px; border-radius: 12px; cursor: pointer; margin: 0 5px;" title="Click to filter by this status">
                                                <span id="badge-out-direct-delivery-text">Direct Delivery (Out for delivery to Consignee)</span> <span id="count-out-direct-delivery" style="background-color: #90EE90; color: black; padding: 2px 6px; border-radius: 8px; margin-left: 4px;"><i class="icon-spinner icon-spin"></i></span>
                                            </span>
                                            
                                            <span class="badge status-filter-badge" data-status="out-domestic-linehaul" style="font-size: 12px; padding: 6px 10px; border-radius: 12px; cursor: pointer; margin: 0 5px;" title="Click to filter by this status">
                                                <span id="badge-out-domestic-linehaul-text">Received at Gateway (Out for delivery to Consignee)</span> <span id="count-out-domestic-linehaul" style="background-color: #0FFF50; color: black; padding: 2px 6px; border-radius: 8px; margin-left: 4px;"><i class="icon-spinner icon-spin"></i></span>
                                            </span>
                                            
                                            <span class="badge" id="status-filter-badge_to_receive" style="font-size: 12px; padding: 6px 10px; border-radius: 12px; cursor: pointer; background-color: #6c757d; color: white; margin-left: 10px;" title="Click to show all statuses">
                                                Show All
                                            </span>
                                            
                                        </div>
                                    </div>
                                </div>
                                
                                <?php } ?>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover" id="toReceiveTable">
                                                <thead>
                                                    <tr>
                                                        <th>Client</th>
                                                        <th>Order #</th>
                                                        <th>Waybill #</th>
                                                        <th>Chargeable Weight</th>
                                                        <th>Destination</th>
                                                        <th>Consignee</th>
                                                        <th>Date</th>
                                                        <th>Days Aging</th>
                                                        <th>Mode of Delivery</th>
                                                        <th>Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <!-- Data will be loaded via AJAX -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Delivered Tab -->
                    <div role="tabpanel" class="tab-pane fade" id="delivered" aria-labelledby="delivered-tab">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h3 class="panel-title">
                                    <i class="fa fa-check-circle text-success"></i> Delivered Shipments (Last 30 Days)
                                    <span style="color: #666; font-size: 14px;">(Shipments that have been successfully delivered)</span>
                                </h3>
                            </div>
                            <div class="panel-body">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover" id="deliveredTable">
                                                <thead>
                                                    <tr>
                                                        <th>Client</th>
                                                        <th>Order #</th>
                                                        <th>Waybill #</th>
                                                        <th>Destination</th>
                                                        <th>Consignee</th>
                                                        <th>Delivered Date</th>
                                                        <th>Mode of Delivery</th>
                                                        <th>Attachment</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <!-- Data will be loaded via AJAX -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- All Delivered Tab -->
                    <div role="tabpanel" class="tab-pane fade" id="allDelivered" aria-labelledby="allDelivered-tab">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h3 class="panel-title">
                                    <i class="fa fa-list text-success"></i> All Delivered Shipments
                                </h3>
                            </div>
                            <div class="panel-body">
                                <div class="row" style="margin-bottom:10px;">
                                    <div class="col-sm-4">
                                        <label>Client</label>
                                        <select class="form-control" id="filterAllDeliveredClient">
                                            <option value="">-- All Clients --</option>
                                        </select>
                                    </div>
                                    <div class="col-sm-3">
                                        <label>Month</label>
                                        <input type="text" class="form-control" id="filterAllDeliveredMonth" placeholder="YYYY-MM">
                                    </div>
                                    <div class="col-sm-3">
                                        <label>&nbsp;</label>
                                        <div>
                                            <button type="button" id="btnAllDeliveredApply" class="btn btn-primary">
                                                <i class="fa fa-filter"></i> Apply
                                            </button>
                                            <button type="button" id="btnAllDeliveredClear" class="btn btn-default">
                                                <i class="fa fa-eraser"></i> Clear
                                            </button>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover" id="allDeliveredTable">
                                                <thead>
                                                    <tr>
                                                        <th>Client</th>
                                                        <th>Order #</th>
                                                        <th>Waybill #</th>
                                                        <th>Destination</th>
                                                        <th>Consignee</th>
                                                        <th>Delivered Date</th>
                                                        <th>Mode of Delivery</th>
                                                        <th>Attachment</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <!-- Data will be loaded via AJAX -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
      
        
        
        $(document).ready(function() {
            var isreadygetdata = 0;
            var statusFilter = 0;
            // Initialize DataTables for each tab
            var prealertedTable = $('#prealertedTable').DataTable({
                "processing": false,
                "serverSide": true,
                "ajax": {
                    "url": "shipments_ajax.php",
                    "type": "POST",
                    "data": function(d) {
                        d.status = "prealerted";
                        d.status_filter = window.currentPrealertedStatusFilter || '';
                        return d;
                    },
                    "beforeSend": function() {
                        if(isreadygetdata==0) { return false; }
                        showCustomLoading();
                    },
                    "complete": function() {}
                },
                "columns": [
                    { "data": "client" },
                    { "data": "order_number" },
                    { "data": "chargeable_weight" },
                    { "data": "destination" },
                    { "data": "consignee" },
                    { "data": "date" },
                    { "data": "schedule_of_pickup" },
                    { "data": "aging" }
                ],
                "pageLength": 25,
                "lengthMenu": [[10, 25, 50, 100], [10, 25, 50, 100]],
                "order": [[6, "asc"]], // Sort by date descending
                "language": {
                    "processing": "Loading data, Please wait...",
                    "emptyTable": "No pre-alerted shipments found",
                    "zeroRecords": "No matching shipments found"
                },
                "autoWidth": false,
                "responsive": true,
                "drawCallback": function (settings) {
                  hideCustomLoading();
                  updatecount(false,'prealerted');
                }
            });
            
            function initprealert() {
              prealertedTable.ajax.reload();
            }
            
            var toShipTable = $('#toShipTable').DataTable({
                "processing": false, // Disable default processing indicator
                "serverSide": true,
                "ajax": {
                    "url": "shipments_ajax.php",
                    "type": "POST",
                    "data": function(d) {
                        d.status = "to_ship";
                        d.status_filter = window.currentStatusFilter || '';
                        return d;
                    },
                    "beforeSend": function() {
                        if(isreadygetdata==0) { return false; }
                        showCustomLoading();
                    },
                    "complete": function() {}
                },
                "columns": [
                    { "data": "client" },
                    { "data": "order_number" },
                    { "data": "waybill_number" },
                    { "data": "chargeable_weight" },
                    { "data": "destination" },
                    { "data": "consignee" },
                    { "data": "date" },
                    { "data": "aging" },
                    { "data": "mode_of_delivery" },
                    { "data": "status" }
                ],
                "pageLength": 25,
                "lengthMenu": [[10, 25, 50, 100], [10, 25, 50, 100]],
                "order": [[7, "asc"]],
                "language": {
                    "processing": "Loading data, Please wait...",
                    "emptyTable": "No shipments ready to ship",
                    "zeroRecords": "No matching shipments found"
                },
                "autoWidth": false,
                "responsive": true,
                "drawCallback": function (settings) {
                  hideCustomLoading();
                  updatecount(false,'to_ship');
                }
            });

            var transferToProviderTable = $('#transferToProviderTable').DataTable({
                "processing": false,
                "serverSide": true,
                "ajax": {
                    "url": "shipments_ajax.php",
                    "type": "POST",
                    "data": function(d) {
                        d.status = "transfer_to_provider";
                        <?php if(IsSP()) { ?>
                        d.status_filter = 'on-the-way-to-gateway';
                        <?php } else { ?>
                        d.status_filter = window.currentTransferToProviderStatusFilter || '';
                        <?php } ?>
                        return d;
                    },
                    "beforeSend": function() {
                        if(isreadygetdata==0) { return false; }
                        showCustomLoading();
                    },
                    "complete": function() { }
                },
                "columns": [
                    { "data": "client" },
                    { "data": "order_number" },
                    { "data": "waybill_number" },
                    { "data": "chargeable_weight" },
                    { "data": "destination" },
                    { "data": "consignee" },
                    { "data": "provider_to_gateway_date" },
                    { "data": "provider_to_gateway_aging" },
                    { "data": "mode_of_delivery" },
                    { "data": "status" }
                ],
                "pageLength": 25,
                "lengthMenu": [[10, 25, 50, 100], [10, 25, 50, 100]],
                "order": [[7, "asc"]],
                "language": {
                    "processing": "Loading data, Please wait...",
                    "emptyTable": "No shipments being transferred to provider",
                    "zeroRecords": "No matching shipments found"
                },
                "autoWidth": false,
                "responsive": true,
                "drawCallback": function (settings) {
                  hideCustomLoading();
                  updatecount(false,'transfer_to_provider');
                }
            });

            var toReceiveTable = $('#toReceiveTable').DataTable({
                "processing": false,
                "serverSide": true,
                "ajax": {
                    "url": "shipments_ajax.php",
                    "type": "POST",
                    "data": function(d) {
                        d.status = "to_receive";
                        d.status_filter = window.currentToReceiveStatusFilter || '';
                        return d;
                    },
                    "beforeSend": function() {
                        if(isreadygetdata==0) { return false; }
                        showCustomLoading();
                    },
                    "complete": function() { }
                },
                "columns": [
                    { "data": "client" },
                    { "data": "order_number" },
                    { "data": "waybill_number" },
                    { "data": "chargeable_weight" },
                    { "data": "destination" },
                    { "data": "consignee" },
                    { "data": "to_receive_date" },
                    { "data": "to_receive_aging" },
                    { "data": "mode_of_delivery" },
                    { "data": "status" }
                ],
                "pageLength": 25,
                "lengthMenu": [[10, 25, 50, 100], [10, 25, 50, 100]],
                "order": [[6, "asc"]],
                "language": {
                    "processing": "Loading data, Please wait...",
                    "zeroRecords": "No matching shipments found"
                },
                "autoWidth": false,
                "responsive": true,
                "drawCallback": function (settings) {
                  hideCustomLoading();
                  updatecount(false,'to_receive');
                }
            });

            var deliveredTable = $('#deliveredTable').DataTable({
                "processing": false,
                "serverSide": true,
                "ajax": {
                    "url": "shipments_ajax.php",
                    "type": "POST",
                    "data": function(d) {
                        d.status = "delivered";
                        return d;
                    },
                    "beforeSend": function() {
                        if(isreadygetdata==0) { return false; }
                        showCustomLoading();
                    },
                    "complete": function() { }
                },
                "columns": [
                    { "data": "client" },
                    { "data": "order_number" },
                    { "data": "waybill_number" },
                    { "data": "destination" },
                    { "data": "consignee" },
                    { "data": "date" },
                    { "data": "mode_of_delivery" },
                    { "data": "status" }
                ],
                "pageLength": 25,
                "lengthMenu": [[10, 25, 50, 100], [10, 25, 50, 100]],
                "order": [[5, "desc"]],
                "language": {
                    "processing": "Loading data, Please wait...",
                    "emptyTable": "No delivered shipments found",
                    "zeroRecords": "No matching shipments found"
                },
                "autoWidth": false,
                "responsive": true,
                "drawCallback": function (settings) {
                  hideCustomLoading();
                  updatecount();
                }
            });

            var allDeliveredTable = $('#allDeliveredTable').DataTable({
                "processing": false,
                "serverSide": true,
                "ajax": {
                    "url": "shipments_ajax.php",
                    "type": "POST",
                    "data": function(d) {
                        d.status = "all_delivered";
                        d.filter_client = $('#filterAllDeliveredClient').val();
                        d.filter_month = $('#filterAllDeliveredMonth').val(); // format: YYYY-MM
                        return d;
                    },
                    "beforeSend": function() {
                        if(isreadygetdata==0) { return false; }
                        showCustomLoading();
                    },
                    "complete": function() { }
                },
                "columns": [
                    { "data": "client" },
                    { "data": "order_number" },
                    { "data": "waybill_number" },
                    { "data": "destination" },
                    { "data": "consignee" },
                    { "data": "date" },
                    { "data": "mode_of_delivery" },
                    { "data": "status" }
                ],
                "pageLength": 25,
                "lengthMenu": [[10, 25, 50, 100], [10, 25, 50, 100]],
                "order": [[5, "desc"]],
                "language": {
                    "processing": "Loading data, Please wait...",
                    "emptyTable": "No delivered shipments found",
                    "zeroRecords": "No matching shipments found"
                },
                "autoWidth": false,
                "responsive": true,
                "drawCallback": function (settings) {
                  hideCustomLoading();
                  loadAllDeliveredClients();
                  updatecount();
                }
                
            });

            // Load All Delivered client list (distinct accounts with delivered records)
            function loadAllDeliveredClients() {
                $.post('shipments_ajax.php', { all_delivered_clients: 1,filter_month: $('#filterAllDeliveredMonth').val() }, function(resp){
                    try {
                        var data = JSON.parse(resp);
                        var $sel = $('#filterAllDeliveredClient');
                        $sel.find('option:not(:first)').remove();
                        if (data && data.clients && data.clients.length) {
                            data.clients.forEach(function(acct){
                                $sel.append('<option value="'+acct.account+'">('+acct.account+') '+acct.name+'</option>');
                            });
                        }
                    } catch(e) {}
                });
            }
            
            // Init month datetimepicker (month picker)
            try {
                $('#filterAllDeliveredMonth').datetimepicker({
                    format: 'YYYY-MM',
                    viewMode: 'months',
                    useCurrent: false
                });
            } catch(e) {
                // fallback: leave as plain text if plugin not available
            }

            $('#btnAllDeliveredApply').on('click', function(e){
                e.preventDefault();
                allDeliveredTable.ajax.reload(null, true); // reset paging
            });
            $('#btnAllDeliveredClear').on('click', function(e){
                e.preventDefault();
                $('#filterAllDeliveredClient').val('');
                $('#filterAllDeliveredMonth').val('');
                allDeliveredTable.ajax.reload(null, true); // reset paging
            });

            function updateBadges(counts) {
                if(!counts) return;
                $('#badge-prealerted').text(counts.prealerted || 0);
                $('#badge-toShip').text(counts.to_ship || 0);
                $('#badge-transferToProvider').text(counts.transfer_to_provider || 0);
                $('#badge-toReceive').text(counts.to_receive || 0);
                $('#badge-delivered').text(counts.delivered || 0);

                // Update status badges in To Ship tab (only SHIPMENTS_TO_SHIP statuses)
                if(counts.status_breakdown) {
                    // Update counts for To Ship tab
                    if(counts.status_breakdown.picked_up) {
                        $('#count-picked-up').text(counts.status_breakdown.picked_up.count || 0);
                        $('#count-scan-hub').text(counts.status_breakdown.scan_hub.count || 0);
                        $('#count-waybill-printed').text(counts.status_breakdown.waybill_printed.count || 0);
                        $('#count-consolidated').text(counts.status_breakdown.consolidated.count || 0);
                        $('#count-dispatched').text(counts.status_breakdown.dispatched.count || 0);
                        $('#count-shipment-dispatched').text(counts.status_breakdown.shipment_dispatched.count || 0);
                    }
                    
                    // Update counts for Pre-alerted tab
                    if(counts.status_breakdown.expired) {
                        $('#count-expired').text(counts.status_breakdown.expired.count || 0);
                        $('#count-current').text(counts.status_breakdown.current.count || 0);
                        $('#count-future').text(counts.status_breakdown.future.count || 0);
                        $('#count-no-schedule').text(counts.status_breakdown['no-schedule'].count || 0);
                    }
                    
                    // Update counts for Transfer to Provider tab
                    if(counts.status_breakdown['on-the-way-to-gateway']) {
                        $('#count-dispatched-tp').text(counts.status_breakdown.dispatched.count || 0);
                        $('#count-on-the-way-to-gateway').text(counts.status_breakdown['on-the-way-to-gateway'].count || 0);
                    }

                    // Update counts for To Receive tab
                    if(counts.status_breakdown['out-direct-delivery'] || counts.status_breakdown['out-domestic-linehaul']) {
                        if(counts.status_breakdown['out-direct-delivery']) {
                            $('#count-out-direct-delivery').text(counts.status_breakdown['out-direct-delivery'].count || 0);
                        }
                        if(counts.status_breakdown['out-domestic-linehaul']) {
                            $('#count-out-domestic-linehaul').text(counts.status_breakdown['out-domestic-linehaul'].count || 0);
                        }
                    }
                }
            }

            // Initial counts fetch
            function updatecount(forceupdate=false,tabclick='') {
              if((isreadygetdata==1) || (forceupdate)) {
              setTimeout(function(){
              $.post('shipments_ajax.php', { counts_only: 1, tab_click:tabclick   }, function(resp){
                  try{ var data = JSON.parse(resp); updateBadges(data.counts); }catch(e){}
              });
              },1000);
              }
            }

            // Initialize tabs
            $('#shipmentTabs a').click(function (e) {
                e.preventDefault();
                $(this).tab('show');
            });

            // Show first tab by default
            $('#shipmentTabs a:first').tab('show');

            // Refresh DataTables when switching tabs to ensure proper rendering and sizing
            $('#shipmentTabs a').on('shown.bs.tab', function (e) {
                var target = $(e.target).attr("href");
                var targetTable = null;
                
                switch(target) {
                    case "#prealerted":
                        targetTable = prealertedTable;
                        break;
                    case "#toShip":
                        targetTable = toShipTable;
                        break;
                    case "#transferToProvider":
                        targetTable = transferToProviderTable;
                        break;
                    case "#toReceive":
                        targetTable = toReceiveTable;
                        break;
                    case "#delivered":
                        targetTable = deliveredTable;
                        break;
                    case "#allDelivered":
                        targetTable = allDeliveredTable;
                        break;
                }
                
                if (targetTable) {
                    // Force DataTables to recalculate column widths
                    setTimeout(function() {
                        targetTable.ajax.reload();
                        targetTable.columns.adjust();
                    }, 100);
                }
            });

            // Status filter functionality for To Ship tab
            window.currentStatusFilter = '';

            function setToShipDateHeader(filter) {
                var label = 'Date';
                switch(filter) {
                    case 'picked_up':
                        label = 'Pickup Date';
                        break;
                    case 'scan_hub':
                        label = 'Receive Date';
                        break;
                    case 'waybill_printed':
                        label = 'Waybill Date';
                        break;
                    case 'consolidated':
                        label = 'Consolidated Date';
                        break;
                    case 'dispatched':
                        label = 'Gatepass Date';
                        break;
                }
                // Update the Date column header; with the new column order it's index 7
                $('#toShipTable thead th').eq(6).text(label);
            }

            $('.status-filter-badge').on('click', function(e) {
                e.preventDefault();
                var statusFilter = $(this).data('status');
                var tabId = $(this).closest('.tab-pane').attr('id');

                // Remove active class from all badges in the same tab
                $(this).closest('.tab-pane').find('.status-filter-badge').removeClass('active');

                // Add active class to clicked badge
                $(this).addClass('active');

                // Set the current filter based on tab
                if(tabId === 'prealerted') {
                    window.currentPrealertedStatusFilter = statusFilter;
                    // Show loading and reload the Pre-alerted table with the filter
                    showCustomLoading();
                    prealertedTable.ajax.reload();
                } else if(tabId === 'toShip') {
                    window.currentStatusFilter = statusFilter;
                    // Show loading and reload the To Ship table with the filter
                    showCustomLoading();
                    setToShipDateHeader(statusFilter);
                    toShipTable.ajax.reload();
                } else if(tabId === 'transferToProvider') {
                    window.currentTransferToProviderStatusFilter = statusFilter;
                    // Show loading and reload the Transfer to Provider table with the filter
                    showCustomLoading();
                    $('#transferToProvider thead th').eq(5).text("Date");
                    if(statusFilter=='on-the-way-to-gateway') {
                      $('#transferToProvider thead th').eq(5).text("Ship Date");
                    }
                    if(statusFilter=='dispatched') {
                      $('#transferToProvider thead th').eq(5).text("Warehouse Scanned Out Date");
                    }
                    transferToProviderTable.ajax.reload();
                } else if(tabId === 'toReceive') {
                    window.currentToReceiveStatusFilter = statusFilter;
                    // Show loading and reload the To Receive table with the filter
                    showCustomLoading();
                    $('#toReceiveTable thead th').eq(5).text("Date");
                    if(statusFilter=='out-domestic-linehaul') {
                      $('#toReceiveTable thead th').eq(5).text("Received at Gateway Date");
                    }
                    if(statusFilter=='out-direct-delivery') {
                      $('#toReceiveTable thead th').eq(5).text("Warehouse Scanned Out Date");
                    }
                    toReceiveTable.ajax.reload();
                }
            });

            $('#clear-status-filter').on('click', function(e) {
                e.preventDefault();
                // Remove active class from all badges
                $('.status-filter-badge').removeClass('active');

                // Clear the filter
                window.currentStatusFilter = '';

                //Show loading and reload the To Ship table without filter
                showCustomLoading();
                setToShipDateHeader('');
                $('#transferToProvider thead th').eq(5).text("Date");
                $('#toReceiveTable thead th').eq(5).text("Date");
                toShipTable.ajax.reload();
            });

            // Status filter functionality for Pre-alerted tab
            window.currentPrealertedStatusFilter = '';

            $('#status-filter-badge_prealerted').on('click', function() {
                // Remove active class from all badges in prealerted tab
                $('#prealerted .status-filter-badge').removeClass('active');

                // Clear the filter
                window.currentPrealertedStatusFilter = '';

                // Show loading and reload the Pre-alerted table without filter
                showCustomLoading();
                prealertedTable.ajax.reload();
            });

            // Status filter functionality for Transfer to Provider tab
            window.currentTransferToProviderStatusFilter = '';

            $('#status-filter-badge_transfer_to_provider').on('click', function() {
                // Remove active class from all badges in transfer to provider tab
                $('#transferToProvider .status-filter-badge').removeClass('active');

                // Clear the filter
                window.currentTransferToProviderStatusFilter = '';

                // Show loading and reload the Transfer to Provider table without filter
                showCustomLoading();
                transferToProviderTable.ajax.reload();
            });

            // Status filter functionality for To Receive tab
            window.currentToReceiveStatusFilter = '';

            $('#status-filter-badge_to_receive').on('click', function() {
                // Remove active class from all badges in to receive tab
                $('#toReceive .status-filter-badge').removeClass('active');

                // Clear the filter
                window.currentToReceiveStatusFilter = '';

                // Show loading and reload the To Receive table without filter
                showCustomLoading();
                toReceiveTable.ajax.reload();
            });
            
            $(window).on("load",function(){ isreadygetdata=1; initprealert(); })
        });

        function ShowpodWaybill(eurl,etitle) {
            eModal.iframe(eurl,etitle);
        }

        function ShowTransDetails(eurl,etitle) {
            var options = {
                url: eurl,
                size: 'lg'
            };
            eModal.iframe(options,etitle);
        }

        //Custom loading overlay functions
        function showCustomLoading(message) {
            message = message || 'Loading data, Please wait...';
            var overlay = $('<div class="custom-loading-overlay">' +
                '<div class="custom-loading-content">' +
                '<div class="custom-loading-spinner"></div>' +
                '<div>' + message + '</div>' +
                '</div>' +
                '</div>');
            $('body').append(overlay);
        }

        function hideCustomLoading() {
            $('.custom-loading-overlay').remove();
        }
    
    </script>
</body> 
