<?php
$_GET['donotshowheader'] = 1;
include('header.php');
include('session.php');

// Initialize query filters
$queryFilters = '';
if(LWLI_CLIENT_ONLINE<>ADMIN_USERS_COMPANY && trim(LWLI_CLIENT_ONLINE)<>'') {
	$queryFilters = " AND tfs.account='".FieldDBInput(LWLI_CLIENT_ONLINE)."'";
}

if(IsClient()) {
  $selectremarks = ' wtr.remarks_note ';
  $filterremarks = ' COALESCE(wtr.remarks_note,"")!="" AND ';
} else {
  $selectremarks = ' wtr.remarks ';
  $filterremarks = ' COALESCE(wtr.remarks,"")!="" AND ';
}

if(IsSP()) {
   $queryFilters .= " AND (EXISTS (SELECT * FROM tf_consolidate tchk WHERE tchk.csmno=tfs.csmno AND tchk.sp_id='".(int)$_SESSION['sp_id']."')) ";
}

if(IsCourierContractor()) {
   $queryFilters .= " AND (EXISTS (SELECT * FROM tf_dispatch tchk WHERE tchk.drno=tfs.drno AND tchk.sfc_id='".(int)$_SESSION['CourierID']."')) ";
}

if(IsCourierDriver() || IsCourierEmployee()) {
   $queryFilters .= " AND (EXISTS (SELECT * FROM tf_dispatch tchk WHERE tchk.drno=tfs.drno AND tchk.sfc_id='".(int)$_SESSION['sp_id']."')) ";
}

// Get status filter from POST request
$statusFilter = '';
$leftJoin = "";
$selectQuery = " '' as mode_of_delivery, '' as destination, '' as release_status ";
$caseSelect = "";
if(isset($_POST['status'])) {
	switch($_POST['status']) {
		case 'prealerted':
			$statusFilter = " AND wtfs.shipment_movement_status = ".PRE_ALERTED_STATUS_ID;
			
			// Add specific status filtering if requested for pre-alerted tab
			if(isset($_POST['status_filter']) && trim($_POST['status_filter']) != '') {
				$statusFilterValue = $_POST['status_filter'];
				switch($statusFilterValue) {
					case 'expired':
						$statusFilter = " AND wtfs.shipment_movement_status = ".PRE_ALERTED_STATUS_ID." AND DATE(wtfs.schedulepickupdate) < CURDATE()";
						break;
					case 'current':
						$statusFilter = " AND wtfs.shipment_movement_status = ".PRE_ALERTED_STATUS_ID." AND DATE(wtfs.schedulepickupdate) = CURDATE()";
						break;
					case 'future':
						$statusFilter = " AND wtfs.shipment_movement_status = ".PRE_ALERTED_STATUS_ID." AND DATE(wtfs.schedulepickupdate) > CURDATE()";
						break;
					case 'no-schedule':
						$statusFilter = " AND wtfs.shipment_movement_status = ".PRE_ALERTED_STATUS_ID." AND ((COALESCE(wtfs.schedulepickupdate,'0000-00-00 00:00:00')='0000-00-00 00:00:00') OR (wtfs.schedulepickupdate = ''))";
						break;
				}
			}
			break;
		case 'to_ship':
			// Exclude shipments that should be in transfer_to_provider based on validation logic
			$statusFilter = " AND ((wtfs.shipment_movement_status IN (".SHIPMENTS_TO_SHIPFILTER.")) ";
			$statusFilter .= "  OR (wtfs.shipment_movement_status = ".SHIPMENT_DISPATCHED_STATUS_ID." AND IFNULL(tfd.release_status,0)= 0))";
			$leftJoin = " LEFT JOIN tf_dispatch tfd ON tfd.drno=tfs.drno";
			$caseSelect = "WHEN wtfs.shipment_movement_status=".SHIPMENT_DISPATCHED_STATUS_ID." AND IFNULL(tfd.release_status, 0) = 0 THEN 'For Dispatch' ";
			$caseSelect = "WHEN wtfs.shipment_movement_status=".SHIPMENT_DISPATCHED_STATUS_ID." AND tfd.release_status = 1 THEN 'Dispatched' ";
			
			$selectQuery = "tfd.destination as destination,tfd.drtype as mode_of_delivery, tfd.release_status as release_status,
						(SELECT CONCAT('<b>',DATE_FORMAT(date_posted, '%d-%b-%y %h:%i %p'),'</b> ',".$selectremarks.") FROM web_tf_remarks wtr WHERE ".$filterremarks." ((wtr.shipment_magic=tfs.billed) OR (COALESCE(wtr.csmno,'')!='' AND tfs.csmno=wtr.csmno) OR (COALESCE(wtr.drno,'')!='' AND wtr.drno=tfs.drno)) AND (wtr.date_posted>=CASE WHEN wtfs.shipment_movement_status=".SHIPMENT_PICKED_UP_STATUS_ID." THEN tfs.pickupdate WHEN wtfs.shipment_movement_status=".SCAN_IN_TO_HUB_STATUS_ID." THEN tfs.rcvdate WHEN wtfs.shipment_movement_status=".WAYBILL_PRINTED_ID." THEN tfs.wbdate WHEN wtfs.shipment_movement_status=".SHIPMENT_CONSOLIDATED_ID." THEN tfs.csmdate WHEN wtfs.shipment_movement_status=".SHIPMENT_DISPATCHED_STATUS_ID." AND IFNULL(tfd.release_status, 0) = 0 THEN tfs.drdate END) ORDER BY wtr.date_posted DESC LIMIT 1) as remarksmess";
		
		    // Add specific status filtering if requested
			if(isset($_POST['status_filter']) && trim($_POST['status_filter']) != '') {
				$statusFilterValue = $_POST['status_filter'];
				switch($statusFilterValue) {
					case 'picked_up':
						$statusFilter = " AND wtfs.shipment_movement_status = ".SHIPMENT_PICKED_UP_STATUS_ID;
						break;
					case 'scan_hub':
						$statusFilter = " AND wtfs.shipment_movement_status = ".SCAN_IN_TO_HUB_STATUS_ID;
						break;
					case 'waybill_printed':
						$statusFilter = " AND wtfs.shipment_movement_status = ".WAYBILL_PRINTED_ID;
						break;
					case 'consolidated':
						$leftJoin = " LEFT JOIN tf_consolidate tfc ON tfc.csmno=tfs.csmno";
						$leftJoin .= " LEFT JOIN tf_dispatch tfd ON tfd.drno=tfs.drno";
						$statusFilter = " AND wtfs.shipment_movement_status = ".SHIPMENT_CONSOLIDATED_ID." AND (COALESCE(tfs.drno,'')='') ";
						break;
					case 'dispatched':
						$caseSelect = "WHEN (wtfs.shipment_movement_status=".SHIPMENT_DISPATCHED_STATUS_ID.") AND (COALESCE(tfs.drno,'')!='') AND IFNULL(tfd.release_status,0) =0 THEN 'For Dispatch' ";
						$selectQuery = "tfd.destination as destination,tfd.drtype as mode_of_delivery, tfd.release_status as release_status";
						$leftJoin = " LEFT JOIN tf_dispatch tfd ON tfd.drno=tfs.drno";
						$statusFilter = " AND (wtfs.shipment_movement_status = ".SHIPMENT_DISPATCHED_STATUS_ID.") AND (COALESCE(tfs.drno,'')!='') ";
						$statusFilter .= " AND IFNULL(tfd.release_status,0) = 0 ";
						break;
					/*case 'shipment_dispatched':
						$caseSelect = "WHEN wtfs.shipment_movement_status=".SHIPMENT_DISPATCHED_STATUS_ID." AND tfd.release_status = 1 THEN 'Dispatched' ";
						$selectQuery = "tfd.destination as destination,tfd.drtype as mode_of_delivery, tfd.release_status as release_status";
						$leftJoin = " LEFT JOIN tf_dispatch tfd ON tfd.drno=tfs.drno";
						$statusFilter = " AND wtfs.shipment_movement_status = ".SHIPMENT_DISPATCHED_STATUS_ID;
						$statusFilter .= " AND tfd.drtype = 'DOMESTIC LINE HAUL TRANSFER' AND tfd.release_status = 1";
						break;*/
				} 
			}
			break;
		case 'transfer_to_provider':
			// Include dispatched shipments and TRANSFER_TO_PROVIDER_ID status
			$leftJoin = " LEFT JOIN tf_dispatch tfd ON tfd.drno=tfs.drno";
			$leftJoin .= " LEFT JOIN web_mf_partners sp ON sp.sfc_id=tfd.sfc_id AND COALESCE(tfd.sfc_id,0)>0 ";
			$statusFilter = " AND ((wtfs.shipment_movement_status = ".SHIPMENT_DISPATCHED_STATUS_ID." AND tfd.drtype = 'DOMESTIC LINE HAUL TRANSFER' AND tfd.release_status = 1)";
			$statusFilter .= " OR (wtfs.shipment_movement_status = ".TRANSFER_TO_PROVIDER_ID." AND tfd.drtype = 'DOMESTIC LINE HAUL TRANSFER'))";
			$caseSelect = "WHEN wtfs.shipment_movement_status=".SHIPMENT_DISPATCHED_STATUS_ID." AND tfd.release_status = 1 THEN 'Dispatched' ";
			$caseSelect .= "WHEN wtfs.shipment_movement_status=".TRANSFER_TO_PROVIDER_ID." THEN 'On the way to gateway' ";
			$selectQuery = "tfd.destination as destination,tfd.drtype as mode_of_delivery, tfd.release_status as release_status, tfd.updateacceptedby, sp.contact_number,
						(SELECT CONCAT('<b>',DATE_FORMAT(date_posted, '%d-%b-%y %h:%i %p'),'</b> ',".$selectremarks.") FROM web_tf_remarks wtr WHERE ".$filterremarks."  ((wtr.shipment_magic=tfs.billed) OR (tfs.csmno=wtr.csmno) OR (wtr.drno=tfs.drno)) AND (wtr.date_posted>=IF(wtfs.shipment_movement_status=".TRANSFER_TO_PROVIDER_ID.",tfs.dispatchrcvddate,tfs.ragdate)) ORDER BY wtr.date_posted DESC LIMIT 1) as remarksmess";
			
			// Add specific status filtering if requested
			if(isset($_POST['status_filter']) && trim($_POST['status_filter']) != '') {
				$statusFilterValue = $_POST['status_filter'];
				switch($statusFilterValue) {
					case 'dispatched':
						$caseSelect = "WHEN wtfs.shipment_movement_status=".SHIPMENT_DISPATCHED_STATUS_ID." AND tfd.release_status = 1 THEN 'Dispatched' ";
						$selectQuery = "tfd.destination as destination,tfd.drtype as mode_of_delivery, tfd.release_status as release_status";
						$leftJoin = " LEFT JOIN tf_dispatch tfd ON tfd.drno=tfs.drno";
						$statusFilter = " AND wtfs.shipment_movement_status = ".SHIPMENT_DISPATCHED_STATUS_ID;
						$statusFilter .= " AND tfd.drtype = 'DOMESTIC LINE HAUL TRANSFER' AND tfd.release_status = 1";
						break;
					case 'on-the-way-to-gateway':
						$caseSelect = "WHEN wtfs.shipment_movement_status=".TRANSFER_TO_PROVIDER_ID." THEN 'On the way to gateway' ";
						$selectQuery = "tfd.destination as destination,tfd.drtype as mode_of_delivery, tfd.release_status as release_status";
						$leftJoin = " LEFT JOIN tf_dispatch tfd ON tfd.drno=tfs.drno";
						$statusFilter = " AND wtfs.shipment_movement_status = ".TRANSFER_TO_PROVIDER_ID;
						$statusFilter .= " AND tfd.drtype = 'DOMESTIC LINE HAUL TRANSFER'";
						break;
				}
			}
			break;
		case 'to_receive':
			$caseSelect = "WHEN wtfs.shipment_movement_status=".SHIPMENT_DISPATCHED_STATUS_ID." AND tfd.release_status = 1 THEN 'Dispatched' ";
			$selectQuery = "tfd.destination as destination,tfd.drtype as mode_of_delivery, tfd.release_status as release_status, tfd.updateacceptedby, sp.contact_number,
			                tfc.spname, spp.contact_number as sp_phone, (SELECT CONCAT('<b>',DATE_FORMAT(date_posted, '%d-%b-%y %h:%i %p'),'</b> ',".$selectremarks.") FROM web_tf_remarks wtr WHERE ".$filterremarks." ((wtr.shipment_magic=tfs.billed) OR (tfs.csmno=wtr.csmno) OR (wtr.drno=tfs.drno)) AND wtr.date_posted>=IF(wtfs.shipment_movement_status=".SHIPMENT_DISPATCHED_STATUS_ID.",tfs.dispatchrcvddate,tfs.ragdate) ORDER BY wtr.date_posted DESC LIMIT 1) as remarksmess ";
			$leftJoin = " LEFT JOIN tf_dispatch tfd ON tfd.drno=tfs.drno";
			$leftJoin .= " LEFT JOIN tf_consolidate tfc ON tfc.csmno=tfs.csmno";
			$leftJoin .= " LEFT JOIN web_mf_partners sp ON sp.sfc_id=tfd.sfc_id";
			$leftJoin .= " LEFT JOIN web_mf_partners spp ON spp.sp_id=tfc.sp_id";
			$statusFilter = " AND (wtfs.shipment_movement_status IN (".SHIPMENTS_TO_RECEIVE.")";
			$statusFilter .= " OR (wtfs.shipment_movement_status=".SHIPMENT_DISPATCHED_STATUS_ID." AND tfd.drtype = 'DIRECT DELIVERY TO CONSIGNEE' AND IFNULL(tfd.release_status, 0) = 1))";
			// Add specific status filtering if requested for To Receive tab
			if(isset($_POST['status_filter']) && trim($_POST['status_filter']) != '') {
				$statusFilterValue = $_POST['status_filter'];
				switch($statusFilterValue) {
					case 'out-direct-delivery':
						$statusFilter = " AND wtfs.shipment_movement_status = ".SHIPMENT_DISPATCHED_STATUS_ID;
						$statusFilter .= " AND tfd.drtype = 'DIRECT DELIVERY TO CONSIGNEE' AND IFNULL(tfd.release_status, 0) = 1";
						break;
					case 'out-domestic-linehaul':
						$statusFilter = " AND wtfs.shipment_movement_status = ".RECEIVED_AT_GATEWAY_STATUS_ID;
						$statusFilter .= " AND tfd.drtype = 'DOMESTIC LINE HAUL TRANSFER'";
						break;
				}
			}
			break;
		case 'delivered':
			
			$statusFilter = " AND wtfs.shipment_movement_status = ".DELIVERED_STATUS_ID.
						  " AND tfs.poddate >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)".
						  " AND tfs.poddate <= CURDATE()";
			$selectQuery = "tfd.destination as destination,tfd.drtype as mode_of_delivery, tfd.release_status as release_status, tfd.updateacceptedby, sp.contact_number,
			                tfc.spname, spp.contact_number as sp_phone ";
            $leftJoin = " LEFT JOIN tf_dispatch tfd ON tfd.drno=tfs.drno";
			$leftJoin .= " LEFT JOIN tf_consolidate tfc ON tfc.csmno=tfs.csmno";
			$leftJoin .= " LEFT JOIN web_mf_partners sp ON sp.sfc_id=tfd.sfc_id";
			$leftJoin .= " LEFT JOIN web_mf_partners spp ON spp.sp_id=tfc.sp_id";
			break;
		case 'all_delivered':
			$statusFilter = " AND wtfs.shipment_movement_status = ".DELIVERED_STATUS_ID;
			// Default to last 6 months if no month filter is specified
			if(!isset($_POST['filter_month']) || trim($_POST['filter_month']) == '') {
				$statusFilter .= " AND tfs.poddate >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)";
			}
			$selectQuery = "tfd.destination as destination,tfd.drtype as mode_of_delivery, tfd.release_status as release_status, tfd.updateacceptedby, sp.contact_number,
			                tfc.spname, spp.contact_number as sp_phone ";
			$leftJoin = " LEFT JOIN tf_dispatch tfd ON tfd.drno=tfs.drno";
			$leftJoin .= " LEFT JOIN tf_consolidate tfc ON tfc.csmno=tfs.csmno";
			$leftJoin .= " LEFT JOIN web_mf_partners sp ON sp.sfc_id=tfd.sfc_id";
			$leftJoin .= " LEFT JOIN web_mf_partners spp ON spp.sp_id=tfc.sp_id";
			break;
		default:
			$statusFilter = " AND wtfs.shipment_movement_status >= 0"; // All statuses
	}
}

// counts_only handling
if(isset($_POST['counts_only'])) {
	$counts = array(
		'prealerted' => 0,
		'to_ship' => 0,
		'transfer_to_provider' => 0,
		'to_receive' => 0,
		'delivered' => 0
	);
	
	if(!IsSP() && !IsCourierDriver() && !IsCourierEmployee() && !IsCourierContractor()) {
  
    $counts['prealerted'] = mysql_result(doQuery("SELECT COUNT(DISTINCT wtfs.shipment_magic) as c FROM tf_shipments tfs LEFT JOIN web_tf_shipments wtfs ON tfs.billed=wtfs.shipment_magic WHERE (COALESCE(tfs.account,'')<>'') AND wtfs.shipment_movement_status=".PRE_ALERTED_STATUS_ID.$queryFilters),0,'c');
	
	$counts['to_ship'] = mysql_result(doQuery("SELECT COUNT(DISTINCT wtfs.shipment_magic) as c FROM tf_shipments tfs LEFT JOIN web_tf_shipments wtfs ON tfs.billed=wtfs.shipment_magic LEFT JOIN tf_dispatch tfd ON tfd.drno=tfs.drno WHERE (COALESCE(tfs.account,'')<>'') AND ((wtfs.shipment_movement_status IN (".SHIPMENTS_TO_SHIPFILTER.")) 
			 OR (wtfs.shipment_movement_status = ".SHIPMENT_DISPATCHED_STATUS_ID." AND IFNULL(tfd.release_status, 0) = 0))".$queryFilters),0,'c');
	
	}
	
	$counts['transfer_to_provider'] = mysql_result(doQuery("SELECT COUNT(DISTINCT wtfs.shipment_magic) as c FROM tf_shipments tfs LEFT JOIN web_tf_shipments wtfs ON tfs.billed=wtfs.shipment_magic LEFT JOIN tf_dispatch tfd ON tfd.drno=tfs.drno INNER JOIN web_mf_accounts wma ON wma.account=tfs.account AND wma.status=0 WHERE (COALESCE(tfs.account,'')<>'') AND ((wtfs.shipment_movement_status = ".SHIPMENT_DISPATCHED_STATUS_ID." AND tfd.drtype = 'DOMESTIC LINE HAUL TRANSFER' AND tfd.release_status = 1) OR (wtfs.shipment_movement_status = ".TRANSFER_TO_PROVIDER_ID." AND tfd.drtype = 'DOMESTIC LINE HAUL TRANSFER'))".$queryFilters),0,'c');
	
	$counts['to_receive'] = mysql_result(doQuery("SELECT COUNT(DISTINCT wtfs.shipment_magic) as c FROM tf_shipments tfs LEFT JOIN web_tf_shipments wtfs ON tfs.billed=wtfs.shipment_magic LEFT JOIN tf_dispatch tfd ON tfd.drno=tfs.drno WHERE (COALESCE(tfs.account,'')<>'') AND (wtfs.shipment_movement_status IN (".SHIPMENTS_TO_RECEIVE.") OR (wtfs.shipment_movement_status=".SHIPMENT_DISPATCHED_STATUS_ID." AND tfd.drtype = 'DIRECT DELIVERY TO CONSIGNEE' AND IFNULL(tfd.release_status, 0) = 1))".$queryFilters),0,'c');
	
	$counts['delivered'] = mysql_result(doQuery("SELECT COUNT(DISTINCT wtfs.shipment_magic) as c FROM tf_shipments tfs LEFT JOIN web_tf_shipments wtfs ON tfs.billed=wtfs.shipment_magic WHERE (COALESCE(tfs.account,'')<>'') AND wtfs.shipment_movement_status=".DELIVERED_STATUS_ID." AND tfs.poddate>=DATE_SUB(CURDATE(), INTERVAL 30 DAY) AND tfs.poddate<=CURDATE()".$queryFilters),0,'c');

	
	$status_breakdown = [];
	if($_POST['tab_click']=='to_ship') {
		// Add detailed status breakdown for To Ship tab (only SHIPMENTS_TO_SHIP statuses)
		$status_breakdown = array(
			'picked_up' => array('count' => 0, 'name' => GiveDeliveryStatus(SHIPMENT_PICKED_UP_STATUS_ID)),
			'scan_hub' => array('count' => 0, 'name' => GiveDeliveryStatus(SCAN_IN_TO_HUB_STATUS_ID)),
			'waybill_printed' => array('count' => 0, 'name' => GiveDeliveryStatus(WAYBILL_PRINTED_ID)),
			'consolidated' => array('count' => 0, 'name' => GiveDeliveryStatus(SHIPMENT_CONSOLIDATED_ID)),
			'dispatched' => array('count' => 0, 'name' => 'For Dispatch'),
			'shipement_dispatched' => array('count' => 0, 'name' => GiveDeliveryStatus(SHIPMENT_DISPATCHED_STATUS_ID))
		);
	
		$baseQuery = "SELECT COUNT(DISTINCT wtfs.shipment_magic) as c FROM tf_shipments tfs LEFT JOIN web_tf_shipments wtfs ON tfs.billed=wtfs.shipment_magic WHERE (COALESCE(tfs.account,'')<>'') AND wtfs.shipment_movement_status=";
		$status_breakdown['picked_up']['count'] = mysql_result(doQuery($baseQuery.SHIPMENT_PICKED_UP_STATUS_ID."".$queryFilters),0,'c');
		$status_breakdown['scan_hub']['count'] = mysql_result(doQuery($baseQuery.SCAN_IN_TO_HUB_STATUS_ID." ".$queryFilters),0,'c');
		$status_breakdown['waybill_printed']['count'] = mysql_result(doQuery($baseQuery.WAYBILL_PRINTED_ID." ".$queryFilters),0,'c');
	
		$baseQuery = "SELECT COUNT(DISTINCT wtfs.shipment_magic) as c FROM tf_shipments tfs LEFT JOIN web_tf_shipments wtfs ON tfs.billed=wtfs.shipment_magic LEFT JOIN tf_dispatch tfd ON tfd.drno=tfs.drno WHERE (COALESCE(tfs.account,'')<>'') AND wtfs.shipment_movement_status=";
		$status_breakdown['consolidated']['count'] = mysql_result(doQuery($baseQuery.SHIPMENT_CONSOLIDATED_ID." AND (COALESCE(tfs.drno,'')='') ".$queryFilters),0,'c');
		
		$status_breakdown['shipment_dispatched']['count'] = mysql_result(doQuery($baseQuery.SHIPMENT_DISPATCHED_STATUS_ID." AND tfd.drtype='DOMESTIC LINE HAUL TRANSFER'".$queryFilters),0,'c');

		$baseQuery = "SELECT COUNT(DISTINCT wtfs.shipment_magic) as c FROM tf_shipments tfs LEFT JOIN web_tf_shipments wtfs ON tfs.billed=wtfs.shipment_magic LEFT JOIN tf_dispatch tfd ON tfd.drno=tfs.drno WHERE (COALESCE(tfs.account,'')<>'') AND ((wtfs.shipment_movement_status=";
		$status_breakdown['dispatched']['count'] = mysql_result(doQuery($baseQuery.SHIPMENT_DISPATCHED_STATUS_ID.") OR (wtfs.shipment_movement_status = ".SHIPMENT_CONSOLIDATED_ID." AND (COALESCE(tfs.drno,'')!=''))) AND IFNULL(tfd.release_status, 0) = 0".$queryFilters),0,'c');

		$counts['status_breakdown'] = $status_breakdown;
	} elseif($_POST['tab_click']=='prealerted') {
		// Add detailed status breakdown for Pre-alerted tab based on schedulepickupdate
		$status_breakdown = array(
			'expired' => array('count' => 0, 'name' => 'Expired'),
			'current' => array('count' => 0, 'name' => 'Current'),
			'future' => array('count' => 0, 'name' => 'Future'),
			'no-schedule' => array('count' => 0, 'name' => 'No assign schedule of pickup date')
		);
	
		$baseQuery = "SELECT COUNT(DISTINCT wtfs.shipment_magic) as c FROM tf_shipments tfs LEFT JOIN web_tf_shipments wtfs ON tfs.billed=wtfs.shipment_magic WHERE (COALESCE(tfs.account,'')<>'') AND wtfs.shipment_movement_status=".PRE_ALERTED_STATUS_ID;
		
		// Expired: schedulepickupdate < current date
		$status_breakdown['expired']['count'] = mysql_result(doQuery($baseQuery." AND DATE(wtfs.schedulepickupdate) < CURDATE()".$queryFilters),0,'c');
		
		// Current: schedulepickupdate = today
		$status_breakdown['current']['count'] = mysql_result(doQuery($baseQuery." AND DATE(wtfs.schedulepickupdate) = CURDATE()".$queryFilters),0,'c');
		
		// Future: schedulepickupdate > current date
		$status_breakdown['future']['count'] = mysql_result(doQuery($baseQuery." AND DATE(wtfs.schedulepickupdate) > CURDATE()".$queryFilters),0,'c');
		
		// No schedule: schedulepickupdate is null or empty
		$status_breakdown['no-schedule']['count'] = mysql_result(doQuery($baseQuery." AND ((COALESCE(wtfs.schedulepickupdate,'0000-00-00 00:00:00')='0000-00-00 00:00:00') OR (wtfs.schedulepickupdate='')) ".$queryFilters),0,'c');
		
		$counts['status_breakdown'] = $status_breakdown;
	} elseif($_POST['tab_click']=='transfer_to_provider') {
		// Add detailed status breakdown for Transfer to Provider tab
		$status_breakdown = array(
			'dispatched' => array('count' => 0, 'name' => 'Dispatched'),
			'on-the-way-to-gateway' => array('count' => 0, 'name' => 'On the way to gateway')
		);
	
		$baseQuery = "SELECT COUNT(DISTINCT wtfs.shipment_magic) as c FROM tf_shipments tfs LEFT JOIN web_tf_shipments wtfs ON tfs.billed=wtfs.shipment_magic LEFT JOIN tf_dispatch tfd ON tfd.drno=tfs.drno WHERE (COALESCE(tfs.account,'')<>'')";
		
		// Dispatched: SHIPMENT_DISPATCHED_STATUS_ID with release_status = 1 and drtype = 'DOMESTIC LINE HAUL TRANSFER'
		$status_breakdown['dispatched']['count'] = mysql_result(doQuery($baseQuery." AND wtfs.shipment_movement_status = ".SHIPMENT_DISPATCHED_STATUS_ID." AND tfd.drtype = 'DOMESTIC LINE HAUL TRANSFER' AND tfd.release_status = 1".$queryFilters),0,'c');
		
		// On the way to gateway: TRANSFER_TO_PROVIDER_ID with drtype = 'DOMESTIC LINE HAUL TRANSFER'
		$status_breakdown['on-the-way-to-gateway']['count'] = mysql_result(doQuery($baseQuery." AND wtfs.shipment_movement_status = ".TRANSFER_TO_PROVIDER_ID." AND tfd.drtype = 'DOMESTIC LINE HAUL TRANSFER'".$queryFilters),0,'c');
		
		$counts['status_breakdown'] = $status_breakdown;
	} elseif($_POST['tab_click']=='to_receive') {
		// Add detailed status breakdown for To Receive tab
		$status_breakdown = array(
			'out-direct-delivery' => array('count' => 0, 'name' => 'Direct Delivery (Out for delivery to Consignee)'),
			'out-domestic-linehaul' => array('count' => 0, 'name' => 'Received at Gateway (Out for delivery to Consignee)')
		);

		$baseQuery = "SELECT COUNT(DISTINCT wtfs.shipment_magic) as c FROM tf_shipments tfs LEFT JOIN web_tf_shipments wtfs ON tfs.billed=wtfs.shipment_magic LEFT JOIN tf_dispatch tfd ON tfd.drno=tfs.drno WHERE (COALESCE(tfs.account,'')<>'')";
		
		// Direct Delivery: Dispatched with Direct Delivery drtype and released
		$status_breakdown['out-direct-delivery']['count'] = mysql_result(doQuery($baseQuery." AND wtfs.shipment_movement_status=".SHIPMENT_DISPATCHED_STATUS_ID." AND tfd.drtype='DIRECT DELIVERY TO CONSIGNEE' AND IFNULL(tfd.release_status,0)=1".$queryFilters),0,'c');
		
		// Received at Gateway (Out domestic linehaul): RECEIVED_AT_GATEWAY_STATUS_ID with DLH
		$status_breakdown['out-domestic-linehaul']['count'] = mysql_result(doQuery($baseQuery." AND wtfs.shipment_movement_status=".RECEIVED_AT_GATEWAY_STATUS_ID." AND tfd.drtype='DOMESTIC LINE HAUL TRANSFER'".$queryFilters),0,'c');

		$counts['status_breakdown'] = $status_breakdown;
	}
	
	echo json_encode(array('counts'=>$counts));
	exit;
}

// Endpoint to fetch client list for All Delivered tab
if(isset($_POST['all_delivered_clients'])) {
	$clients = array();
	if(!isset($_POST['filter_month']) || trim($_POST['filter_month']) == '') {
		$statusFilter .= " AND tfs.poddate >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)";
	}
	$q = doQuery("SELECT DISTINCT tfs.account AS acct,mf.name FROM tf_shipments tfs LEFT JOIN web_tf_shipments wtfs ON tfs.billed=wtfs.shipment_magic
				 LEFT JOIN mf_accounts mf ON mf.account=tfs.account 
				 WHERE (COALESCE(tfs.account,'')<>'') AND wtfs.shipment_movement_status=".DELIVERED_STATUS_ID.$queryFilters.$statusFilter." ORDER BY tfs.account");
	while($r = mysql_fetch_array($q)) {
		$clients[] = ['account' => $r['acct'],
					  'name' => $r['name']];
	}
	echo json_encode(array('clients'=>$clients));
	exit;
}

// Count query for total records
$querysqlAll = "SELECT count(DISTINCT tfs.billed) as counter
                FROM tf_shipments tfs
				LEFT JOIN web_tf_shipments wtfs on tfs.billed=wtfs.shipment_magic
				".$leftJoin."
				WHERE (COALESCE(tfs.account,'')<>'') AND wtfs.shipment_magic=tfs.billed ".$statusFilter.$queryFilters;

// Main query for data
$querysqlfinal = "SELECT
						 tfs.account as client,
						 tfs.orderno as order_number,
						 tfs.waybillno as waybill_number,
						 tfs.csmno as csmno,
						 IF((ISNULL(tfs.pickupdate)) or (trim(tfs.pickupdate) = '0000-00-00'),tfs.rcvdate,tfs.pickupdate) as pickup_date,
						 tfs.poddate,
						 wtfs.shipment_magic as shipment_magic,
						 ROUND(IF(tfs.wgtvolume > 0, IF((tfs.wgtvolume/".VOLUME_METRIC_DIVISOR.") > tfs.wgt,(tfs.wgtvolume/".VOLUME_METRIC_DIVISOR."),tfs.wgt),
						  IF(((tfs.len*tfs.wid*tfs.hgt)/".VOLUME_METRIC_DIVISOR.") > tfs.wgt,((tfs.len*tfs.wid*tfs.hgt)/".VOLUME_METRIC_DIVISOR."),tfs.wgt)),2) as chargable_weight,
						 wtfs.schedulepickupdate,
						 DATEDIFF(CURDATE(), DATE(wtfs.schedulepickupdate)) as aging_days,
						 CASE
						    WHEN (wtfs.shipment_movement_status=".SHIPMENT_PICKED_UP_STATUS_ID.") THEN tfs.pickupdate
						    WHEN (wtfs.shipment_movement_status=".SCAN_IN_TO_HUB_STATUS_ID.") THEN tfs.rcvdate
						    WHEN (wtfs.shipment_movement_status= ".WAYBILL_PRINTED_ID.") THEN tfs.wbdate
						    WHEN (wtfs.shipment_movement_status= ".SHIPMENT_CONSOLIDATED_ID.") THEN tfs.csmdate ".
						((isset($_POST['status']) && $_POST['status']=='to_ship' && $_POST['status_filter']='dispatched') ? "
							WHEN (wtfs.shipment_movement_status=".SHIPMENT_DISPATCHED_STATUS_ID." AND IFNULL(tfd.release_status,0)=0) THEN tfs.drdate " : "").
						"	ELSE wtfs.createdDateTime
						 END as toship_date,
						 CASE
						    WHEN (wtfs.shipment_movement_status=".SHIPMENT_PICKED_UP_STATUS_ID.") THEN DATEDIFF(CURDATE(), DATE(tfs.pickupdate))
						    WHEN (wtfs.shipment_movement_status=".SCAN_IN_TO_HUB_STATUS_ID.") THEN DATEDIFF(CURDATE(), DATE(tfs.rcvdate))
						    WHEN (wtfs.shipment_movement_status=".WAYBILL_PRINTED_ID.") THEN DATEDIFF(CURDATE(), DATE(tfs.wbdate))
						    WHEN (wtfs.shipment_movement_status=".SHIPMENT_CONSOLIDATED_ID.") THEN DATEDIFF(CURDATE(), DATE(tfs.csmdate)) ".
						((isset($_POST['status']) && $_POST['status']=='to_ship' && $_POST['status_filter']='dispatched') ? "   
													WHEN (wtfs.shipment_movement_status= ".SHIPMENT_DISPATCHED_STATUS_ID." AND IFNULL(tfd.release_status,0)=0) THEN DATEDIFF(CURDATE(), DATE(tfs.drdate)) " : "") .
					   "    ELSE DATEDIFF(CURDATE(), DATE(wtfs.createdDateTime))
						 END as toship_aging_days,
						 CASE
						    WHEN (wtfs.shipment_movement_status=".SHIPMENT_DISPATCHED_STATUS_ID." AND ((COALESCE(tfs.shipdate,'0000-00-00 00:00:00')='0000-00-00 00:00:00') OR (tfs.shipdate=''))) THEN tfs.dispatchrcvddate 
						    WHEN (wtfs.shipment_movement_status=".TRANSFER_TO_PROVIDER_ID." AND COALESCE(tfs.shipdate,'0000-00-00 00:00:00')!='0000-00-00 00:00:00' AND (tfs.shipdate!='')) THEN tfs.shipdate 
					     END as provider_to_gateway_date,
						 CASE
						    WHEN (wtfs.shipment_movement_status=".SHIPMENT_DISPATCHED_STATUS_ID." AND ((COALESCE(tfs.shipdate,'0000-00-00 00:00:00')='0000-00-00 00:00:00') OR (tfs.shipdate=''))) THEN DATEDIFF(CURDATE(), DATE(tfs.dispatchrcvddate))
						    WHEN (wtfs.shipment_movement_status=".TRANSFER_TO_PROVIDER_ID." AND COALESCE(tfs.shipdate,'0000-00-00 00:00:00')!='0000-00-00 00:00:00') THEN DATEDIFF(CURDATE(), DATE(tfs.shipdate))
					     END as provider_to_gateway_aging,".
						 ((isset($_POST['status']) && $_POST['status']=='to_receive') ? "
						 CASE
						    WHEN (wtfs.shipment_movement_status=".SHIPMENT_DISPATCHED_STATUS_ID.") THEN tfs.dispatchrcvddate 
						    WHEN (wtfs.shipment_movement_status=".RECEIVED_AT_GATEWAY_STATUS_ID." ) THEN tfs.ragdate 
					     END as to_receive_date,
						 CASE
						    WHEN (wtfs.shipment_movement_status=".SHIPMENT_DISPATCHED_STATUS_ID.") THEN DATEDIFF(CURDATE(), DATE(tfs.dispatchrcvddate))
						    WHEN (wtfs.shipment_movement_status=".RECEIVED_AT_GATEWAY_STATUS_ID." ) THEN DATEDIFF(CURDATE(), DATE(tfs.ragdate))
					     END as to_receive_aging,
						 " : "'' as to_receive_date,0 as to_receive_aging,").
						"tfs.consignee as consignee,
						 tfs.cityprov as city_province,
                         tfs.brgymun,
						 tfs.street,
                         tfs.zipcode,
						 tfs.status as remarks,
                         tfs.createdon,
						 wtfs.shipment_movement_status as status_index,
						 CASE
						    WHEN wtfs.shipment_movement_status=".DELIVERED_STATUS_ID." THEN '".GiveDeliveryStatus(DELIVERED_STATUS_ID)."'
							WHEN wtfs.shipment_movement_status=".RECEIVED_AT_GATEWAY_STATUS_ID." THEN '".GiveDeliveryStatus(RECEIVED_AT_GATEWAY_STATUS_ID)."'
							WHEN wtfs.shipment_movement_status=".TRANSFER_TO_PROVIDER_ID." THEN '".GiveDeliveryStatus(TRANSFER_TO_PROVIDER_ID)."'
							".$caseSelect."
							WHEN wtfs.shipment_movement_status=".SHIPMENT_CONSOLIDATED_ID." THEN '".GiveDeliveryStatus(SHIPMENT_CONSOLIDATED_ID)."'
							WHEN wtfs.shipment_movement_status=".WAYBILL_PRINTED_ID." THEN '".GiveDeliveryStatus(WAYBILL_PRINTED_ID)."'
							WHEN wtfs.shipment_movement_status=".SCAN_IN_TO_HUB_STATUS_ID." THEN '".GiveDeliveryStatus(SCAN_IN_TO_HUB_STATUS_ID)."'
							WHEN wtfs.shipment_movement_status=".SHIPMENT_PICKED_UP_STATUS_ID." THEN '".GiveDeliveryStatus(SHIPMENT_PICKED_UP_STATUS_ID)."'
							WHEN wtfs.shipment_movement_status=".CANCELLED_STATUS_ID." THEN '".GiveDeliveryStatus(CANCELLED_STATUS_ID)."'
							WHEN wtfs.shipment_movement_status=".PRE_ALERTED_STATUS_ID." THEN '".GiveDeliveryStatus(PRE_ALERTED_STATUS_ID)."'
                         END as status_word,
						 wtfs.createdDateTime,
						 tfs.createdby,
						 ".$selectQuery."
     		FROM tf_shipments tfs
			LEFT JOIN web_tf_shipments wtfs on tfs.billed=wtfs.shipment_magic
			".$leftJoin."
			WHERE (COALESCE(tfs.account,'')<>'') ".$statusFilter.$queryFilters;

// Search functionality
$querywheresearch = '';
if(isset($_REQUEST['search']['value']) && trim($_REQUEST['search']['value']) != '') {
	$tfdq = '';
	if($leftJoin!='') {
	  $tfdq = " OR (tfd.destination LIKE '%".$searchValue."%')";
	}
	$searchValue = FieldDBInput($_REQUEST['search']['value']);
	$querywheresearch = " AND ((tfs.orderno LIKE '%".$searchValue."%') OR (tfs.waybillno LIKE '%".$searchValue."%')
	                         OR (tfs.consignee LIKE '%".$searchValue."%') OR (tfs.account LIKE '%".$searchValue."%')
							 OR (wtfs.modeofdelivery LIKE '%".$searchValue."%')".$tfdq.")";
}

// Add extra filters for all_delivered
$extraAllDelivered = '';
if(isset($_POST['status']) && $_POST['status'] == 'all_delivered') {
	if(isset($_POST['filter_client']) && trim($_POST['filter_client']) != '') {
		$extraAllDelivered .= " AND tfs.account LIKE '%".FieldDBInput($_POST['filter_client'])."%'";
	}
	if(isset($_POST['filter_month']) && trim($_POST['filter_month']) != '') {
		$monthVal = $_POST['filter_month']; // expected YYYY-MM from datetimepicker
		if(preg_match('/^\\d{4}-\\d{2}$/', $monthVal)) {
			$ym = FieldDBInput($monthVal);
			$extraAllDelivered .= " AND tfs.poddate >= CONCAT('".$ym."','-01 00:00:00') AND tfs.poddate < DATE_ADD(CONCAT('".$ym."','-01 00:00:00'), INTERVAL 1 MONTH)";
		}
	}
}

// Get total count
$queryAll = doQuery($querysqlAll . $querywheresearch.$extraAllDelivered);
$totalData = 0;
if(mysql_num_rows($queryAll) > 0) {
	$totalData = mysql_result($queryAll, 0, 'counter');
}

// Add search to main query
$querysqlfinal .= $querywheresearch.$extraAllDelivered;

// Ordering
$orderby = '';
if(isset($_REQUEST['order']) && is_array($_REQUEST['order'])) {
	for($xx = 0; $xx < sizeof($_REQUEST['order']); $xx++) {
		if($orderby<>'') {
			$orderby .= ',';
		}
		$columnIndex = (int)$_REQUEST['order'][$xx]['column'];
		$direction = $_REQUEST['order'][$xx]['dir'];
		
		// Map column index to actual column names based on the tab/status
		if($_POST['status'] == 'prealerted') {
			// Pre-alerted Tab (8 columns)
			switch($columnIndex) {
				case 0: // Client
					$orderby .= "tfs.account ".$direction;
					break;
				case 1: // Order #
					$orderby .= "tfs.orderno ".$direction;
					break;
				case 2: // Chargeable Weight
					$orderby .= "chargable_weight ".$direction;
					break;
				case 3: // Destination
					$orderby .= "TRIM(CONCAT(tfs.street,tfs.brgymun,tfs.cityprov,tfs.zipcode)) ".$direction;
					break;
				case 4: // Consignee
					$orderby .= "tfs.consignee ".$direction;
					break;
				case 5: // Pre-alert Date
					$orderby .= "wtfs.createdDateTime ".$direction;
					break;
				case 6: // Schedule of Pickup Date
					$orderby .= "wtfs.schedulepickupdate ".$direction;
					break;
				case 7: // Days Aging
					$orderby .= "wtfs.schedulepickupdate ".$direction;
					break;
				default:
					$orderby .= "wtfs.createdDateTime DESC";
			}
		} elseif($_POST['status'] == 'to_ship' || $_POST['status'] == 'transfer_to_provider' || $_POST['status'] == 'to_receive') {
			// To Ship, Transfer to Provider, To Receive Tabs (10 columns)
			switch($columnIndex) {
				case 0: // Client
					$orderby .= "tfs.account ".$direction;
					break;
				case 1: // Order #
					$orderby .= "tfs.orderno ".$direction;
					break;
				case 2: // Waybill #
					$orderby .= "tfs.waybillno ".$direction;
					break;
				case 3: // Chargeable Weight
					$orderby .= "chargable_weight ".$direction;
					break;
				case 4: // Destination
					$orderby .= "tfd.destination ".$direction;
					break;
				case 5: // Consignee
					$orderby .= "tfs.consignee ".$direction;
					break;
				case 6: // Date
					if($_POST['status'] == 'to_receive') {
						$orderby .= "to_receive_date ".$direction;
					} elseif($_POST['status'] == 'transfer_to_provider') {
						$orderby .= "provider_to_gateway_date ".$direction;
					} else { // to_ship
						$orderby .= "toship_date ".$direction;
					}
					break;
				case 7: // Days Aging
					if($_POST['status'] == 'to_receive') {
						$orderby .= "to_receive_aging ".$direction;
					} elseif($_POST['status'] == 'transfer_to_provider') {
						$orderby .= "provider_to_gateway_date ".$direction;
					} else { // to_ship
						$orderby .= "toship_date ".$direction;
					}
					break;
				case 8: // Mode of Delivery
					$orderby .= "wtfs.modeofdelivery ".$direction;
					break;
				case 9: // Status
					$orderby .= "wtfs.shipment_movement_status ".$direction;
					break;
				default:
					$orderby .= "wtfs.createdDateTime DESC";
			}
		} elseif($_POST['status'] == 'delivered' || $_POST['status'] == 'all_delivered') {
			// Delivered and All Delivered Tabs (8 columns)
			switch($columnIndex) {
				case 0: // Client
					$orderby .= "tfs.account ".$direction;
					break;
				case 1: // Order #
					$orderby .= "tfs.orderno ".$direction;
					break;
				case 2: // Waybill #
					$orderby .= "tfs.waybillno ".$direction;
					break;
				case 3: // Destination
					$orderby .= "tfd.destination ".$direction;
					break;
				case 4: // Consignee
					$orderby .= "tfs.consignee ".$direction;
					break;
				case 5: // Delivered Date
					$orderby .= "tfs.poddate ".$direction;
					break;
				case 6: // Mode of Delivery
					$orderby .= "wtfs.modeofdelivery ".$direction;
					break;
				case 7: // Attachment (not sortable, but included for completeness)
					// Attachment column is typically not sortable
					$orderby .= "tfs.poddate ".$direction;
					break;
				default:
					$orderby .= "tfs.poddate DESC";
			}
		} else {
			// Default case
			switch($columnIndex) {
				case 0: // Client
					$orderby .= "tfs.account ".$direction;
					break;
				case 1: // Order #
					$orderby .= "tfs.orderno ".$direction;
					break;
				case 2: // Waybill #
					$orderby .= "tfs.waybillno ".$direction;
					break;
				default:
					$orderby .= "wtfs.createdDateTime DESC";
			}
		}
	}
}

if($orderby<>'') {
	$orderby = " ORDER BY ".$orderby;
} else {
	$orderby = " ORDER BY wtfs.createdDateTime DESC";
}

// Pagination
$limitoffset = " LIMIT ".(int)$_REQUEST['start'].", ".(int)$_REQUEST['length'];
if((int)$_REQUEST['start'] == 0 && (int)$_REQUEST['length'] == 0) {
	$limitoffset = '';
}

// Final query
$querysqlfinal .= " GROUP BY wtfs.shipment_magic " . $orderby . $limitoffset;
//echo $querysqlfinal; exit;
$query = doQuery($querysqlfinal);

$data = array();
while($row = mysql_fetch_array($query)) {
	$destination = '';
	if(str_replace([',',' '],'',$row['street']) != '') {
		$destination .= (($destination!='') ? ', ' : '') . $row['street'];
	}
	if(str_replace([',',' '],'',$row['brgymun']) != '') {
		$destination .= (($destination!='') ? ', ' : '') . $row['brgymun'];
	}
	if(str_replace([',',' '],'',$row['city_province']) != '') {
		$destination .= (($destination!='') ? ', ' : '')  . $row['city_province'];
	}
	if(str_replace([',',' '],'',$row['zipcode']) != '') {
		$destination .= ' ' . $row['zipcode'];
	}
	
	if(isset($_POST['status']) && $_POST['status'] == 'prealerted') {
		$date = FormatDate($row['createdon'], 5);
	} elseif(isset($_POST['status']) && ($_POST['status'] == 'delivered' || $_POST['status'] == 'all_delivered')) {
		$date = FormatDate($row['poddate'], 5);
	} else {
		$date = FormatDate($row['createdDateTime'], 5);
	}
	
	$statusClass = '';

	// Check if we're in specific tabs where all statuses should be green
	$greenTabs = array('to_ship', 'transfer_to_provider', 'to_receive');
	if(isset($_POST['status']) && in_array($_POST['status'], $greenTabs)) {
		$statusClass = 'label-success'; // Green for all statuses in these tabs
	} else {
		// Use different colors for other tabs (delivered, all_delivered, prealerted)
		switch($row['status_index']) {
			case PRE_ALERTED_STATUS_ID:
				$statusClass = 'label-warning'; // Orange/Yellow
				break;
			case SHIPMENT_PICKED_UP_STATUS_ID:
				$statusClass = 'label-info'; // Light Blue
				break;
			case SCAN_IN_TO_HUB_STATUS_ID:
				$statusClass = 'label-primary'; // Blue
				break;
			case WAYBILL_PRINTED_ID:
				$statusClass = 'label-success'; // Green
				break;
			case SHIPMENT_CONSOLIDATED_ID:
				$statusClass = 'label-default'; // Gray
				break;
			case SHIPMENT_DISPATCHED_STATUS_ID:
				$statusClass = 'label-danger'; // Red
				break;
			case TRANSFER_TO_PROVIDER_ID:
				$statusClass = 'label-warning'; // Orange/Yellow
				break;
			case RECEIVED_AT_GATEWAY_STATUS_ID:
				$statusClass = 'label-info'; // Light Blue
				break;
			case DELIVERED_STATUS_ID:
				$statusClass = 'label-success'; // Green
				break;
			case CANCELLED_STATUS_ID:
				$statusClass = 'label-danger'; // Red
				break;
			default:
				$statusClass = 'label-default'; // Gray
		}
	}
	
	// Build status HTML
	if(isset($_POST['status']) && ($_POST['status'] == 'delivered' || $_POST['status'] == 'all_delivered')) {
		$statusHtml = '';
	} else {
		$statusHtml = '<span class="label '.$statusClass.'">'.$row['status_word'].'</span>';
	}
	if($row['status_index'] == DELIVERED_STATUS_ID && (isset($_POST['status']) && ($_POST['status'] == 'delivered' || $_POST['status'] == 'all_delivered'))) {
		$podTitle = 'Attachment of Waybill No.: '.($row['waybill_number'] != '' ? $row['waybill_number'] : '');
		$podUrl = 'tab_podimage.php?podimagemagic='.$row['shipment_magic'].'&attachmenttype=3';
		$statusHtml .= '<br><a href="javascript:ShowpodWaybill(\''.$podUrl.'\',\''.$podTitle.'\')" title="View POD"><i class="icon-picture icon-large"></i> POD</a>';
	}
	
	// Make waybill clickable for specific tabs
	$waybillDisplay = '';
	if($row['waybill_number'] != '') {
		$clickableTabs = array('to_ship', 'transfer_to_provider', 'to_receive', 'delivered', 'all_delivered');
		if(isset($_POST['status']) && in_array($_POST['status'], $clickableTabs)) {
			$waybillDisplay = '<a href="javascript:ShowTransDetails(\'waybill_PDF.php?shipment_magic='.$row['shipment_magic'].'\',\'Details of Order#'.$row['order_number'].'\')" title="View Waybill Details">'.$row['waybill_number'].'</a>';
		} else {
			$waybillDisplay = $row['waybill_number'];
		}
	} else {
		$waybillDisplay = '<span class="text-muted">Pending</span>';
	}
	
	$spinfo = '';
	if(($_POST['status']=='delivered') || ($_POST['status']=='all_delivered')) {
		$phone=GiveMobileFormat($row['contact_number']);
		$addspinfo = '';
		if($row['mode_of_delivery']=='DOMESTIC LINE HAUL TRANSFER') {
		  $addspinfo = ' & LAST MILE by <strong  class="text-danger"> '.$row['spname'].' '.GiveMobileFormat($row['sp_phone']).'</strong>';
		}
		$spinfo .= (($row['updateacceptedby']!='') ? ' by <strong class="text-info">'.$row['updateacceptedby'].(($phone!='') ? '&nbsp;'.$phone : '').'</strong>' : '') . $addspinfo;
	}
	
	if(($_POST['status']=='to_receive') || ($_POST['status']=='transfer_to_provider')) {
		$phone=GiveMobileFormat($row['contact_number']);
	
		if($_POST['status']=='transfer_to_provider') {
		    $eta='';
            $ata='';
			$trackdata = '';
			$r = doQuery("SELECT DISTINCT h.tracking_number,h.*,sfc.* FROM web_tf_consolidate_transport_history h
											   LEFT JOIN sf_codes sfc on sfc.ctype='LINEHAUL' AND sfc.value1=h.consolidator
						  WHERE h.csmno='".FieldDBInput($row['csmno'])."' order by id");
			
			$counter=0;
			while($csd = mysql_fetch_array($r)) {
			 
			   $counter++;
			   $trknum = $csd['tracking_number'];
			   if(trim($csd['tracking_num_prefix'])!='') { 
				  if(stripos($trknum,$csd['tracking_num_prefix'])!==false) {
					$trknum = trim(substr($trknum,stripos($trknum,$csd['tracking_num_prefix'])+strlen($csd['tracking_num_prefix'])+1,strlen($trknum)));
				  } 
			   }
			  
			   if($csd['tracking_url']!='') {
					 $urld = $csd['tracking_url'];
					 if($row['destination']=='PAL EXPRESS') {
					   $urld = str_replace('AWBNo=','AWBNo='.$trknum,$urld);
					 }
					 $trackdata .= (($trackdata!='') ? '<br>' : '') . '<a title="'.$row['provider'].' Track & Trace" href="'.$urld.'" target="_blank">'.$trknum.'</a>';
			   } else {
				   $trackdata .= (($trackdata!='') ? '<br>' : '') . '<span title="Tracking Number">'.$trknum.'</span>';
			   }
			 
			   if($counter==1) {
				 $eta=$csd['eta'];
				 $ata=$csd['ata'];
			   }
			}
			
            $otherinfo = (($trackdata!='') ? '<br>Tracking Number: '.$trackdata : '').
			             ((trim($eta)!='' && trim($eta)!='0000-00-00') ? '<br>ETA: '.FormatDate($eta.' 00:00:00') : '').
						 ((trim($ata)!='' && trim($ata)!='0000-00-00 00:00:00') ? '<br>ATA: '.FormatDate($ata.' 00:00:00') : '');
			
			$spinfo .= (($row['destination']!='') ? ' @ '.$row['destination'].$otherinfo.'<br>' : '');
		}
		
		$addspinfo = '';
		if($_POST['status']=='to_receive' && $row['status_index'] == RECEIVED_AT_GATEWAY_STATUS_ID) {
		   $addspinfo = ' & LAST MILE by <strong  class="text-danger"> '.$row['spname'].' '.GiveMobileFormat($row['sp_phone']).'</strong>';
		}
		
        $spinfo .= (($row['updateacceptedby']!='') ? ' by <strong class="text-info">'.$row['updateacceptedby'].(($phone!='') ? '&nbsp;'.$phone : '').'</strong>' : '').$addspinfo.'<br>';
	}

	// Pre-alerted specific fields
	$scheduleOfPickup = '<span class="text-muted">-</span>';
	$agingDisplay = '<span class="text-muted">-</span>';
	if(isset($_POST['status']) && $_POST['status'] == 'prealerted') {
		if(isset($row['schedulepickupdate']) && trim($row['schedulepickupdate']) != '' && trim($row['schedulepickupdate']) != '0000-00-00 00:00:00') {
			$scheduleOfPickup = FormatDate($row['schedulepickupdate'], 5);
		}
		if(isset($row['aging_days']) && $row['aging_days'] !== null) {
			$agingDisplay = (int)$row['aging_days'];
		}
	}
	
	$remarks = '';
	if(isset($row['remarksmess']) && trim($row['remarksmess'])!='') {
      $remarks = '<br><a style="display:inline-block;padding-top:5px;" title="View All Remarks" href="javascript:ShowTransDetails(\'add_remarksv2.php?viewremarks=1&transtype=5&datal='.(int)$row['shipment_magic'].'\',\'Waybill Number:  '.$row['waybill_number'].'\')"><i class="icon-plus-sign-alt icon-large"></i></a>'.
	  '&nbsp;'.$row['remarksmess'];
	}
					 
	$data[] = array(
		'order_number' => str_replace(",",", ",$row['order_number']),
		'waybill_number' => $waybillDisplay,
		'chargeable_weight' => (isset($_POST['status']) && ($_POST['status']=='prealerted' || $_POST['status']=='to_ship' || $_POST['status']=='transfer_to_provider' || $_POST['status']=='to_receive')) ? number_format((float)$row['chargable_weight'], 2) : '',
		'client' => $row['client'],
		'destination' => $destination,
		'consignee' => $row['consignee'],
		'date' => (isset($_POST['status']) && $_POST['status']=='to_ship') ? FormatDate($row['toship_date'],5) : $date,
		'schedule_of_pickup' => $scheduleOfPickup,
		'aging' => (isset($_POST['status']) && $_POST['status']=='to_ship') ? (is_null($row['toship_aging_days']) ? '<span class="text-muted">-</span>' : (int)$row['toship_aging_days']) : $agingDisplay,
		'provider_to_gateway_aging' => $row['provider_to_gateway_aging'],
		'provider_to_gateway_date' => FormatDate($row['provider_to_gateway_date'],5),
		'to_receive_date' => FormatDate($row['to_receive_date'],5),
		'to_receive_aging' => $row['to_receive_aging'],
		'mode_of_delivery' => ($row['mode_of_delivery'] != '' ? $row['mode_of_delivery'] : '<span class="text-muted">-</span>').$spinfo,
		'status' => $statusHtml.$remarks
	);
}

// Return JSON response for DataTables
$json_data = array(
	"draw"            => intval($_REQUEST['draw']),
	"recordsTotal"    => intval($totalData),
	"recordsFiltered" => intval($totalData),
	"data"            => $data,
	"counts"          => []
);

echo json_encode($json_data);
?>
