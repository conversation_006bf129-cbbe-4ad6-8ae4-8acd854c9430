<?php
if(isset($_GET['dynamicDataProvider'])) {
  $_GET['donotshowheader'] = 1;
}

include('header.php');
include('session.php');

?>

<body>
    <style>
        /* Status badge styling */
        .badge {
            transition: all 0.2s ease;
            cursor: pointer;
            white-space: nowrap;
        }
        .badge:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        .badge-info {
            background-color: #5bc0de !important;
        }
        .badge-primary {
            background-color: #337ab7 !important;
        }
        /* Custom styling for badges */
        .status-badge-black {
            background-color: #000 !important;
            color: #ffff00 !important;
        }
        .status-count-red {
            background-color: #dc3545 !important;
            color: white !important;
            padding: 2px 6px;
            border-radius: 8px;
            margin-left: 4px;
            font-weight: bold;
        }
        /* Status filter badge styling */
        .status-filter-badge {
            transition: all 0.3s ease;
        }
        .status-filter-badge:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .status-filter-badge.active {
            background-color: #007bff !important;
            color: white !important;
            box-shadow: 0 4px 8px rgba(0,123,255,0.3);
        }

        /* Custom loading overlay */
        .custom-loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }

        .custom-loading-content {
            background-color: #fff;
            padding: 30px 40px;
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            color: #333;
        }

        .custom-loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007bff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Hide default DataTables processing indicator */
        .dataTables_processing {
            display: none !important;
        }

        /* Enhanced status label colors */
        .label {
            font-size: 11px;
            font-weight: bold;
            padding: 4px 8px;
            border-radius: 4px;
            text-transform: uppercase;
        }

        .label-info {
            background-color: #5bc0de !important; /* Light Blue */
            color: white !important;
        }

        .label-success {
            background-color: #5cb85c !important; /* Green */
            color: white !important;
        }

        .label-warning {
            background-color: #f0ad4e !important; /* Orange */
            color: white !important;
        }

        .label-danger {
            background-color: #d9534f !important; /* Red */
            color: white !important;
        }

        .label-primary {
            background-color: #337ab7 !important; /* Blue */
            color: white !important;
        }

        .label-default {
            background-color: #777 !important; /* Gray */
            color: white !important;
        }
    </style>
    <?php include('navbar_menu.php'); ?>
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <br><br><br>

                <h3 class="page-header">Pre-alert Dispatching</h3>

                <!-- Tab Navigation -->
                <ul class="nav nav-tabs" id="dispatchingTabs" role="tablist">
                    <li role="presentation" class="active">
                        <a href="#directDelivery" id="directDelivery-tab" role="tab" data-toggle="tab" aria-controls="directDelivery" aria-expanded="true">
                            <i class="fa fa-truck"></i> Direct Delivery
                            <span class="badge" id="badge-directDelivery"><i class="icon-spinner icon-spin"></i></span>
                        </a>
                    </li>

                    <li role="presentation">
                       <a href="#pickupDeliver" id="pickupDeliver-tab" role="tab" data-toggle="tab" aria-controls="pickupDeliver" aria-expanded="false">
                            <i class="fa fa-exchange"></i> Pickup & Deliver
                            <span class="badge" id="badge-pickupDeliver"><i class="icon-spinner icon-spin"></i></span>
                       </a>
                    </li>
                </ul>
                </br>
                <!-- Tab Content -->
                <div class="tab-content" id="dispatchingTabContent">
                    <!-- Direct Delivery Tab -->
                    <div role="tabpanel" class="tab-pane fade in active" id="directDelivery" aria-labelledby="directDelivery-tab">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h3 class="panel-title">
                                    <i class="fa fa-truck text-primary"></i> Direct Delivery Shipments
                                    <span style="color: #666; font-size: 14px;">(Shipments for direct delivery to consignee)</span>
                                </h3>
                            </div>
                            <div class="panel-body">
                                <!-- Status Badges Row -->
                                <div class="row" style="margin-bottom: 20px;">
                                    <div class="col-md-12">
                                        <div style="display: flex; flex-wrap: wrap; gap: 8px; align-items: center; justify-content: center;">
                                            <span class="badge status-filter-badge" data-status="ready-dispatch" style="font-size: 12px; padding: 6px 10px; border-radius: 12px; cursor: pointer; margin: 0 5px;" title="Click to filter by this status">
                                                <span id="badge-ready-dispatch-text">Ready for Dispatch</span> <span id="count-ready-dispatch" style="background-color: #90EE90; color: black; padding: 2px 6px; border-radius: 8px; margin-left: 4px;"><i class="icon-spinner icon-spin"></i></span>
                                            </span>
                                            <span class="badge status-filter-badge" data-status="dispatched" style="font-size: 12px; padding: 6px 10px; border-radius: 12px; cursor: pointer; margin: 0 5px;" title="Click to filter by this status">
                                                <span id="badge-dispatched-text">Dispatched</span> <span id="count-dispatched" style="background-color: #0FFF50; color: black; padding: 2px 6px; border-radius: 8px; margin-left: 4px;"><i class="icon-spinner icon-spin"></i></span>
                                            </span>
                                            <span class="badge" id="clear-direct-delivery-filter" style="font-size: 12px; padding: 6px 10px; border-radius: 12px; cursor: pointer; background-color: #6c757d; color: white; margin-left: 10px;" title="Click to show all statuses">
                                                Show All
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover" id="directDeliveryTable">
                                                <thead>
                                                    <tr>
                                                        <th>Client</th>
                                                        <th>Order #</th>
                                                        <th>Waybill #</th>
                                                        <th>Chargeable Weight</th>
                                                        <th>Destination</th>
                                                        <th>Consignee</th>
                                                        <th>Date</th>
                                                        <th>Days Aging</th>
                                                        <th>Courier</th>
                                                        <th>Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <!-- Data will be loaded via AJAX -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pickup & Deliver Tab -->
                    <div role="tabpanel" class="tab-pane fade" id="pickupDeliver" aria-labelledby="pickupDeliver-tab">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h3 class="panel-title">
                                    <i class="fa fa-exchange text-warning"></i> Pickup & Deliver Shipments
                                    <span style="color: #666; font-size: 14px;">(Shipments for pickup and delivery through service partners)</span>
                                </h3>
                            </div>
                            <div class="panel-body">
                                <!-- Status Badges Row -->
                                <div class="row" style="margin-bottom: 20px;">
                                    <div class="col-md-12">
                                        <div style="display: flex; flex-wrap: wrap; gap: 8px; align-items: center; justify-content: center;">
                                            <span class="badge status-filter-badge" data-status="ready-consolidate" style="font-size: 12px; padding: 6px 10px; border-radius: 12px; cursor: pointer; margin: 0 5px;" title="Click to filter by this status">
                                                <span id="badge-ready-consolidate-text">Ready for Consolidation</span> <span id="count-ready-consolidate" style="background-color: #90EE90; color: black; padding: 2px 6px; border-radius: 8px; margin-left: 4px;"><i class="icon-spinner icon-spin"></i></span>
                                            </span>
                                            <span class="badge status-filter-badge" data-status="consolidated" style="font-size: 12px; padding: 6px 10px; border-radius: 12px; cursor: pointer; margin: 0 5px;" title="Click to filter by this status">
                                                <span id="badge-consolidated-text">Consolidated</span> <span id="count-consolidated" style="background-color: #0FFF50; color: black; padding: 2px 6px; border-radius: 8px; margin-left: 4px;"><i class="icon-spinner icon-spin"></i></span>
                                            </span>
                                            <span class="badge" id="clear-pickup-deliver-filter" style="font-size: 12px; padding: 6px 10px; border-radius: 12px; cursor: pointer; background-color: #6c757d; color: white; margin-left: 10px;" title="Click to show all statuses">
                                                Show All
                                            </span>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="table-responsive">
                                            <table class="table table-striped table-hover" id="pickupDeliverTable">
                                                <thead>
                                                    <tr>
                                                        <th>Client</th>
                                                        <th>Order #</th>
                                                        <th>Waybill #</th>
                                                        <th>Chargeable Weight</th>
                                                        <th>Destination</th>
                                                        <th>Consignee</th>
                                                        <th>Date</th>
                                                        <th>Days Aging</th>
                                                        <th>Service Partner</th>
                                                        <th>Status</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <!-- Data will be loaded via AJAX -->
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            var isreadygetdata = 0;

            // Initialize DataTables for each tab
            var directDeliveryTable = $('#directDeliveryTable').DataTable({
                "processing": false,
                "serverSide": true,
                "ajax": {
                    "url": "prealert_dispatching_ajax.php",
                    "type": "POST",
                    "data": function(d) {
                        d.status = "direct_delivery";
                        d.status_filter = window.currentDirectDeliveryStatusFilter || '';
                        return d;
                    },
                    "beforeSend": function() {
                        if(isreadygetdata==0) { return false; }
                        showCustomLoading();
                    },
                    "complete": function() {}
                },
                "columns": [
                    { "data": "client" },
                    { "data": "order_number" },
                    { "data": "waybill_number" },
                    { "data": "chargeable_weight" },
                    { "data": "destination" },
                    { "data": "consignee" },
                    { "data": "date" },
                    { "data": "aging" },
                    { "data": "courier" },
                    { "data": "status" }
                ],
                "pageLength": 25,
                "lengthMenu": [[10, 25, 50, 100], [10, 25, 50, 100]],
                "order": [[7, "asc"]], // Sort by aging ascending
                "language": {
                    "processing": "Loading data, Please wait...",
                    "emptyTable": "No direct delivery shipments found",
                    "zeroRecords": "No matching shipments found"
                },
                "autoWidth": false,
                "responsive": true,
                "drawCallback": function (settings) {
                    hideCustomLoading();
                    updatecount(false,'direct_delivery');
                }
            });

            var pickupDeliverTable = $('#pickupDeliverTable').DataTable({
                "processing": false,
                "serverSide": true,
                "ajax": {
                    "url": "prealert_dispatching_ajax.php",
                    "type": "POST",
                    "data": function(d) {
                        d.status = "pickup_deliver";
                        d.status_filter = window.currentPickupDeliverStatusFilter || '';
                        return d;
                    },
                    "beforeSend": function() {
                        if(isreadygetdata==0) { return false; }
                        showCustomLoading();
                    },
                    "complete": function() {}
                },
                "columns": [
                    { "data": "client" },
                    { "data": "order_number" },
                    { "data": "waybill_number" },
                    { "data": "chargeable_weight" },
                    { "data": "destination" },
                    { "data": "consignee" },
                    { "data": "date" },
                    { "data": "aging" },
                    { "data": "service_partner" },
                    { "data": "status" }
                ],
                "pageLength": 25,
                "lengthMenu": [[10, 25, 50, 100], [10, 25, 50, 100]],
                "order": [[7, "asc"]], // Sort by aging ascending
                "language": {
                    "processing": "Loading data, Please wait...",
                    "emptyTable": "No pickup & deliver shipments found",
                    "zeroRecords": "No matching shipments found"
                },
                "autoWidth": false,
                "responsive": true,
                "drawCallback": function (settings) {
                    hideCustomLoading();
                    updatecount(false,'pickup_deliver');
                }
            });

            function updateBadges(counts) {
                if(!counts) return;
                $('#badge-directDelivery').text(counts.direct_delivery || 0);
                $('#badge-pickupDeliver').text(counts.pickup_deliver || 0);

                // Update status badges for Direct Delivery tab
                if(counts.status_breakdown) {
                    if(counts.status_breakdown['ready-dispatch']) {
                        $('#count-ready-dispatch').text(counts.status_breakdown['ready-dispatch'].count || 0);
                        $('#count-dispatched').text(counts.status_breakdown.dispatched.count || 0);
                    }

                    // Update status badges for Pickup & Deliver tab
                    if(counts.status_breakdown['ready-consolidate']) {
                        $('#count-ready-consolidate').text(counts.status_breakdown['ready-consolidate'].count || 0);
                        $('#count-consolidated').text(counts.status_breakdown.consolidated.count || 0);
                    }
                }
            }

            function updatecount(forceupdate, tabname) {
                if(forceupdate || isreadygetdata == 1) {
                    $.post('prealert_dispatching_ajax.php', {
                        counts_only: 1,
                        tab_click: tabname || 'direct_delivery'
                    }, function(resp){
                        try {
                            var data = JSON.parse(resp);
                            updateBadges(data);
                        } catch(e) {}
                    });
                }
            }

            // Custom loading functions
            function showCustomLoading() {
                if ($('.custom-loading-overlay').length === 0) {
                    $('body').append('<div class="custom-loading-overlay"><div class="custom-loading-content"><div class="custom-loading-spinner"></div>Loading data, please wait...</div></div>');
                }
            }

            function hideCustomLoading() {
                $('.custom-loading-overlay').remove();
            }

            // Tab click handlers
            $('#dispatchingTabs a[data-toggle="tab"]').on('shown.bs.tab', function (e) {
                var target = $(e.target).attr("href");
                if(target == '#directDelivery') {
                    directDeliveryTable.ajax.reload();
                    updatecount(true, 'direct_delivery');
                } else if(target == '#pickupDeliver') {
                    pickupDeliverTable.ajax.reload();
                    updatecount(true, 'pickup_deliver');
                }
            });

            // Status filter handlers for Direct Delivery
            $('.status-filter-badge[data-status]').on('click', function() {
                var status = $(this).data('status');
                var tabId = $(this).closest('.tab-pane').attr('id');

                if(tabId == 'directDelivery') {
                    window.currentDirectDeliveryStatusFilter = status;
                    directDeliveryTable.ajax.reload();
                } else if(tabId == 'pickupDeliver') {
                    window.currentPickupDeliverStatusFilter = status;
                    pickupDeliverTable.ajax.reload();
                }

                // Update active state
                $(this).siblings('.status-filter-badge').removeClass('active');
                $(this).addClass('active');
            });

            // Clear filter handlers
            $('#clear-direct-delivery-filter').on('click', function() {
                window.currentDirectDeliveryStatusFilter = '';
                directDeliveryTable.ajax.reload();
                $(this).siblings('.status-filter-badge').removeClass('active');
            });

            $('#clear-pickup-deliver-filter').on('click', function() {
                window.currentPickupDeliverStatusFilter = '';
                pickupDeliverTable.ajax.reload();
                $(this).siblings('.status-filter-badge').removeClass('active');
            });

            // Initialize data loading
            setTimeout(function() {
                isreadygetdata = 1;
                directDeliveryTable.ajax.reload();
                updatecount(true, 'direct_delivery');
            }, 1000);
        });
    </script>
</body>
</html>