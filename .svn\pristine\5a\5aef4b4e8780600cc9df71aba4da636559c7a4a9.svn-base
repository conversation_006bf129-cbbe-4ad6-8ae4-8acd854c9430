<?php
include('header.php');
//create password utility
if(isset($_GET['createlwlipassword']) && $_GET['createlwlipassword']==1) {
   $p = encrypt_password($_GET['pwd']);
   echo 'YOUR PWD:    '.$p.'<br>';
   exit;
}
include('session.php');
?>
<style>
.btn-default {
    padding: 8px 17px;
    text-align: center;
    font-size: 16px;
}
.p {
	 background-color: none;
	 color: white;
	 border: none;
	 width:85%;
}

.badge {
	 background-color: red;
	 color: white;
	 border: none;
}
</style>
<body>
<?php include('navbar_menu.php'); ?>
<div id="headerfixedmenuspacer">&nbsp;</div>

<div class="container-fluid">
	<ul class="nav nav-tabs nav-tabs-margin-top">
		<li class="active"><a href="#"> &nbsp;Status Report</a></li>
	</ul>
</br>

<div class="tab-content">
	<div class="row">
      <div class='col-sm-2'>
         <div class="form-group">
         	<label>pickup date from</label>
            <div class='input-group date' id='pickupdate_from'>
               <input type='text' class="form-control" id='pickupdatefrom' value="<?php echo ($_GET['from']<>'') ? $_GET['from'] : '' ?>" />
               <span class="input-group-addon">
               <span class="glyphicon glyphicon-calendar"></span>
               </span>
            </div>
         </div>
      </div>
      <script type="text/javascript">
         $(function () {
             $('#pickupdate_from').datetimepicker({
             	format: 'YYYY-MM-DD'
             });
         });
      </script>
      <div class='col-sm-2'>
         <div class="form-group">
         	<label>pickup date to</label>
            <div class='input-group date' id='pickupdate_to'>
               <input type='text' class="form-control" id='pickupdateto'value="<?php echo ($_GET['to']<>'') ? $_GET['to'] : '' ?>"/>
               <span class="input-group-addon">
               <span class="glyphicon glyphicon-calendar"></span>
               </span>
            </div>
         </div>
      </div>
      <script type="text/javascript">
         $(function () {
             $('#pickupdate_to').datetimepicker({
             	format: 'YYYY-MM-DD'
             });
         });
      </script>
      <div class='col-sm-2'>
      	<label style="visibility: hidden">filter</label>
      	<div class="form-group">
      	 <button type="button" class="btn btn-primary" onclick="filter_btn()">Filter</button>
      </div>
      </div>
   </div>
	<div class="tab-pane fade in active" id="tabstatusreport">
	<table  cellpadding="0" cellspacing="0" class="display table table-bordered" width="100%" id="statusreport">
		<thead>
            <tr>
				<th>Account</th>
				<th>Date Pickup</th>
                <th>Scanned In Date</th>
                <th>Scanned Out Date</th>
				<th>No. of Days (From Scan In to Scan Out)</th>
                <th>Shipping Date</th>
                <th>Provider</th>
                <th>Receive Gateway Date</th>
				<th>Received by</th>
                <th>Delivery Date</th>
                <th>Billing Invoice</th>
				<th>Waybill Number</th>
				<th>DR Number</th>
				<th>Order Number</th>
				<th>Items</th>
				<th>Quantity</th>
				<th>Actual Weight</th>
				<th>CBM</th>
				<th>Measurement</th>
				<th>Volumetric Charge</th>
				<th>Chargeable Weight</th>
				<th>Consignee</th>
				<th>City/Province</th>
				<th>Address</th>
				<th>Courier</th>
				<th>Remarks</th>
				<th>Status</th>
			</tr>
        </thead>
    </table>


	<script type="text/javascript">
         <!--
        function filter_btn(){
        	var pickupdate_from=$('#pickupdatefrom').val();
        	var pickupdate_to=$('#pickupdateto').val();
        	var new_url='<?php echo Domain_Location ?>'+'/status_report.php';
        	var url = new_url+'?from='+pickupdate_from+'&to='+pickupdate_to;
        	//document.location.replace(document.location.search);
        	
        	document.location = url;
        }

		$(document).ready( function() {
		$('#statusreport').DataTable({
			"lengthMenu": [ [10, 25, 50, -1], [10, 25, 50, "All"] ],
			"scrollY": 500,
			"scrollX": true,
			"processing": true,
			"serverSide": true,
			"order": [[1, "desc"]],
       /* <?php if($_SESSION['ShowColumnDateUpdate']==1) { ?>
			"order": [[23, "desc"], [2, "asc"], [0, "asc"]],
		<?php } else { ?>
			"order": [[23, "asc"], [1, "asc"], [2, "asc"]],
		<?php } ?>*/
			//"pageLength": 50,
			"dom": 'lBfrtip',
		"buttons": [
            {
                extend: 'collection',
                text: 'Export',
                buttons: [
                    'copy',
                    'excel',
                    'csv',
                    'pdf',
                    'print'
                ]
            }
        ],
			"ajax":{

			url :"tab_grid_data_status_report.php?from=<?php echo $_GET['from']?>&to=<?php echo $_GET['to'] ?>",
			type: "post",
			error: function(){  
		$(".statusreport-error").html("");
		$("#statusreport_processing").css("display","none");
		/*$("#statusreport").append('<tbody class="employee-grid-error">'+
									'<tr><th colspan="18">'+
									'No data found in the server'+
					 				'</th></tr></tbody>');
					  */					
			}
			}
		});
		});
		
		function ShowTransDetails(eurl,etitle) {
               eModal.iframe(eurl,etitle);
        }
 		//-->
    </script> 
	</div>
	
</div>
</div>
<?php include('footer.php'); ?>
